# Analysis of Stata Project for WHO Financial Protection Monitoring

This project is designed to process microdata from household surveys to produce and report on Sustainable Development Goal (SDG) indicator 3.8.2 (financial protection against catastrophic health expenditure) and related statistics for the World Health Organization (WHO). The analysis is performed using Stata.

## Core Objectives:

1.  **Produce SDG 3.8.2 Estimates**: Calculate the main SDG 3.8.2 indicator, which measures financial hardship due to out-of-pocket (OOP) health payments.
2.  **Component Analysis**: Analyze the components contributing to the SDG 3.8.2 indicator.
3.  **Disaggregation**: Provide disaggregated results by various household characteristics, including:
    *   Expenditure quintiles
    *   Rural/urban residence
    *   Household head's age and gender
    *   Household typology (e.g., age/gender composition of members)
4.  **Standardized Reporting**: Generate standardized Excel reports for each processed country/survey.

## Project Structure and Workflow:

The project is organized into several directories containing Stata `.do` files and data:

*   `0-Working data/`: Contains working datasets, including an example `PPPfactors20241012_TEST.dta`.
*   `2-Do/`: Contains master control scripts and data preparation templates.
    *   `CC25SDG382&related_Master_ISO3C.do`: The main master script that orchestrates the entire analysis pipeline.
    *   `01_Prepare_ISO3C_YYYY_v0s.do`: A template script that needs to be customized for each specific input survey microdataset to prepare key variables.
*   `3-WHO standardized do MS/`: Contains standardized Stata scripts provided by WHO for core calculations and reporting. These are generally not meant to be modified by the user.
    *   `FPCC_SDG382revShort_MS.do`: Calculates the revised SDG 3.8.2 indicator and related financial protection statistics.
    *   `FPCC_HHD typology.do`: Likely defines household typologies for disaggregation.
    *   `FPCC_agedisaggregationn123.do`: Performs disaggregation by age.
    *   `CC Table 2025.do`: Exports the final results into formatted Excel tables.
    *   `Titles.xlsx`: An Excel file containing predefined titles, notes, and headers for the output reports.
*   `4-Results/`: Stores the output of the analysis.
    *   `Excel/`: Contains the final Excel reports.
    *   `Meso_data/`: Contains intermediate Stata datasets (`.dta`) called "meso data," which are aggregated results from the microdata.

### Detailed Workflow based on Key Scripts:

1.  **Master Control (`2-Do/CC25SDG382&related_Master_ISO3C.do`)**:
    *   **Setup**: Initializes Stata, sets memory, and installs necessary packages (optional).
    *   **User Configuration**: Requires manual input for user details, paths to raw microdata, and the location of the "STATA PACKAGE May 2025" folder.
    *   **Survey Definition**: Requires manual input to list the surveys to be processed (e.g., `ISO_2009`).
    *   **Microdata Preparation**: For each survey, it calls a corresponding `01_Prepare_[ISO_YEAR]_v0s.do` script. This script (which the user must adapt) standardizes variables from the raw survey microdata (e.g., household expenditure, health expenditure, household size, recall periods, PPP factors).
    *   **Indicator Production**:
        *   Uses the prepared data (`[ISO_YEAR]_v0s.dta`).
        *   Calls `3-WHO standardized do MS/FPCC_SDG382revShort_MS.do` to calculate SDG 3.8.2 and related indicators.
        *   Saves the results as "meso" data (e.g., `[iso3c]_mesoallv_[year].dta` and `[iso3c]_meso_[year].dta`).
    *   **Meso Data Listing**: Requires manual input to list all generated "mesoallv" datasets that need further processing.
    *   **Disaggregation**:
        *   For each "mesoallv" dataset:
            *   Calls `3-WHO standardized do MS/FPCC_HHD typology.do` (likely for classifying households).
            *   Calls `3-WHO standardized do MS/FPCC_agedisaggregationn123.do` (for age-based disaggregation).
        *   Saves the updated meso datasets.
    *   **Final Meso Data Consolidation**: If multiple years are processed, this section (requiring manual input) appends the year-specific meso datasets into consolidated files (e.g., `[iso3c]_meso.dta`). For a single year, it renames the files.
    *   **Export Results**: Calls `3-WHO standardized do MS/CC Table 2025.do` to generate the final Excel report.

2.  **SDG 3.8.2 Calculation (`3-WHO standardized do MS/FPCC_SDG382revShort_MS.do`)**:
    *   **Variable Generation**: Standardizes expenditure data to per capita per day, calculates expenditure net of OOP, and determines median expenditures.
    *   **Societal Poverty Line (SPL)**:
        *   Calculates a relative poverty line (50% of median consumption, both net and gross of OOP).
        *   Calculates the Joliffe & Prydz SPL (combining $1.15 PPP 2017 with 50% median consumption).
        *   Determines the final SPL as the maximum of an extreme poverty line (e.g., $2.15 PPP 2017) and the calculated SPL.
    *   **Discretionary Consumption**: Calculates consumption available after meeting basic needs (defined by the SPL).
    *   **Financial Hardship Indicators**:
        *   Identifies households with OOP health spending exceeding 100% of their discretionary consumption (impoverishing).
        *   Identifies households with OOP health spending exceeding 40% of their discretionary consumption (catastrophic).
        *   Categorizes households into types of financial hardship (e.g., "pushed into poverty," "further pushed into poverty," "catastrophic but not impoverished").
    *   **Statistics Production**: Calculates prevalence rates for these indicators, disaggregated by quintiles and, if available, rural/urban residence.
    *   **Labeling and Renaming**: Applies descriptive labels and renames variables for clarity and consistency.

3.  **Excel Report Generation (`3-WHO standardized do MS/CC Table 2025.do`)**:
    *   **Data Cleaning for Reporting**: Replaces certain Stata missing values or specific codes with "NA" (Not Available) or "NQ" (Not Quotable/Not Published) based on data quality flags or specific conditions.
    *   **Report Generation (per country)**:
        *   Creates an Excel file named `[iso3c]_UHC financial protection indicators 2025_EN.xlsx`.
        *   **"Revised SDG 3.8.2" Sheet**: Populates this sheet with key SDG 3.8.2 indicators and their disaggregations. Uses `Titles.xlsx` for headers and notes. Applies extensive formatting (fonts, colors, alignment, borders, merged cells).
        *   **"Ancillary statistics" Sheet**: Populates this sheet with supporting statistics (e.g., median consumption, poverty lines used, population shares). Also uses `Titles.xlsx` and applies extensive formatting.
        *   **"Result data table" Sheet**: Exports a broader set of calculated variables in a less formatted table.

## Main Functions Summarized:

*   **Data Ingestion and Preparation**: Standardizes raw survey microdata from various sources into a consistent format.
*   **Poverty Line Calculation**: Establishes appropriate poverty lines (both absolute and societal/relative) for assessing financial hardship.
*   **Financial Hardship Measurement**: Quantifies the extent of catastrophic and impoverishing health expenditures using established WHO methodologies (SDG 3.8.2).
*   **Disaggregated Analysis**: Breaks down financial protection indicators across different socio-demographic and economic subgroups to identify vulnerable populations.
*   **Automated Reporting**: Generates structured and formatted Excel reports presenting the findings for each country/survey.

## Dependencies:

*   Stata (version supporting `mata`, `xl()` class, and potentially specific user-installed packages like `missings`, `tabstatmat`, `distinct`).
*   User-provided raw microdata for each survey.
*   User-customized preparation scripts (`01_Prepare_ISO3C_YYYY_v0s.do`) for each survey.
*   The `Titles.xlsx` file for report formatting.

This Stata package provides a comprehensive toolkit for countries or researchers to analyze financial protection and report on SDG 3.8.2 in a standardized manner consistent with WHO guidelines.
