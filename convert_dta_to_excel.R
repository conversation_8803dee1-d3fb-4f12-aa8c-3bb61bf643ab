# R script to read specific .dta files, merge them, and save as .xlsx and .csv

# Install packages if not already installed
if (!requireNamespace("haven", quietly = TRUE)) {
  install.packages("haven")
}
if (!requireNamespace("writexl", quietly = TRUE)) {
  install.packages("writexl")
}
if (!requireNamespace("dplyr", quietly = TRUE)) {
  install.packages("dplyr")
}
if (!requireNamespace("readr", quietly = TRUE)) { # For write_csv
  install.packages("readr")
}

# Load necessary libraries
library(haven)
library(writexl)
library(dplyr)
library(readr) # For write_csv

# Define the input directory
# Assumes the script is run from the parent directory of "0-Working data/"
# or that the working directory is set appropriately.
input_directory <- "0-Working data"

# Define the specific .dta files to read
files_to_read <- c(
  "TEST_YYYY_SRVA_CONSEXP.dta",
  "TEST_YYYY_SRVA_HEALTHEXP.dta",
  "TEST_YYYY_SRVA_HHD.dta"
  # "TEST_YYYY_SRVA_HHMEMBERS.dta"
)

# Construct full paths to the files
file_paths <- file.path(input_directory, files_to_read)

# Check if all specified files exist
files_exist <- sapply(file_paths, file.exists)
if (!all(files_exist)) {
  message("Error: Not all specified .dta files were found in the directory: ", input_directory)
  message("Missing file(s):")
  for (i in seq_along(files_exist)) {
    if (!files_exist[i]) {
      message("- ", files_to_read[i])
    }
  }
} else {
  message("Found all specified .dta files. Starting the process...")

  tryCatch({
    # Read the .dta files into a list of data frames
    data_frames <- lapply(file_paths, read_dta)
    names(data_frames) <- tools::file_path_sans_ext(files_to_read) # Optional: name list elements

    # Perform sequential full joins on 'hhid_str'
    # Ensure 'hhid_str' exists in all data frames before joining
    # For simplicity, this example assumes 'hhid_str' is present and correctly named.
    # A more robust script would add checks for the existence of 'hhid_str' in each df.

    if (length(data_frames) < 2) {
      message("Error: Need at least two data frames to perform a join.")
      merged_data <- data_frames[[1]] # If only one file, this is the "merged" data
    } else {
      # Start with the first data frame
      merged_data <- data_frames[[1]]
      
      # Sequentially join the rest of the data frames
      for (i in 2:length(data_frames)) {
        # Check if hhid_str is in both dataframes
        if (!("hhid_str" %in% names(merged_data))) {
            stop(paste("Error: 'hhid_str' not found in the accumulated merged data before joining with", names(data_frames)[i]))
        }
        if (!("hhid_str" %in% names(data_frames[[i]]))) {
            stop(paste("Error: 'hhid_str' not found in", names(data_frames)[i]))
        }
        merged_data <- full_join(merged_data, data_frames[[i]], by = "hhid_str")
        message("Joined with: ", names(data_frames)[i])
      }
    }

    # Define output file paths (in the input directory)
    output_excel_path <- file.path(input_directory, "merged_survey_data.xlsx")
    output_csv_path <- file.path(input_directory, "merged_survey_data.csv")

    # Save the merged data to Excel
    write_xlsx(merged_data, path = output_excel_path)
    message("Successfully saved merged data to '", basename(output_excel_path), "'")

    # Save the merged data to CSV
    write_csv(merged_data, path = output_csv_path)
    message("Successfully saved merged data to '", basename(output_csv_path), "'")

  }, error = function(e) {
    message("An error occurred: ", e$message)
  })

  message("\nProcess finished.")
}
