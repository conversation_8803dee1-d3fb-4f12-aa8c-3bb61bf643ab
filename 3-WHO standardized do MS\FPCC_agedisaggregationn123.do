****************************************************************************************************************************************
*Objective: Produce revised SDG 3.8.2 statistics by age of the household head, sex of the household head and age composition of the household
*********************************************************************************************************************************************
*[DO NOT MODIFY ANY CONTENT IN THIS DO FILE]
* PRECONDITION: RUN FIRST FPCC_HHD typology.do

*************************************************************************************
* Version: May 24, 2025 
* Produced by WHO HQ-Financial protection monitoring team
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (FPCC_agedisaggregation123.do)
*****************************************************************************************/


***************************************************************************************************************************************************************
* DEFINE INDICATORS OF INTEREST

glo indicators_gmr25 "FHD_SPL100 FHD_SPL40" 

***************************************************************************************************************************************************************
* DEFINE CODES FOR NON-VALID CASES

label define nonvalid 99 "not found" 98 "less than 15obs" 96 "missing rate>10%" 97 "5%-10% missing rate & dif in indicator>1%",modify


***************************************************************************************************************************************************************
* CHECK IF SAMPLE WITH 5%-10% MISSING RATE FOR CATEGORY OF INTEREST LEAD TO DIFFERENT INCIDENCE RATES THAN WITH THE FULL SAMPLE

*foreach name in age hh1_agey hh1_gender{  /// hay un error en el codigo no hay _ en el nombre de las variables
		 
foreach name in age hh1age hh1gender{
	capture confirm variable check_`name'id
	if !_rc{
		sum flag_`name'
		if r(mean)==96{
			* CHECK IF 5-10% missing  & indicator change by > 1%   -->> take value 97
			foreach var in $indicators_gmr25 { 
			sum `var'
			local a=r(mean)
			sum `var' if check_`name'id==0
			local b=r(mean)
			local c=abs(`a' - `b')
				if `c'>=0.01{
				gen `var'_`name'=97 
				}
				else{
				gen `var'_`name'=`var' if check_`name'id==0
					}
				} 
		 }
		else{
			foreach var in $indicators_gmr25 {
			gen `var'_`name'=`var' if check_`name'id==0
			replace `var'_`name'=96 if flag_`name'==96
			}
		}
	}
	else{
	 display "information on `name' is not available"
	}
}


***************************************************************************************************************************************************************

* AGE STRUCTURE OF THE HHD

if inlist(flag_age,99,98,96){
	forvalues i=1/7{
		foreach var of varlist $indicators_gmr25{
		 gen `var'_t`i'=flag_age
		}
		qui gen  hhw_t`i'=flag_age
		qui gen popw_t`i'=flag_age

	}
}
else{
	count if ~missing(hhtype)&hhtype~=99
	if r(N)~=0{
	replace popweight=round(hhw*hhsize_age,1)
		levelsof hhtype, clean local(hhtype) 
		foreach j of local hhtype{
		local lt1	"Only adults" 
		local lt2	"Adults 20-59y with children and adolescents <=19y"
		local lt3	"Multigenerational households"  
		local lt4	"Adults 20-59y with old dependents (>=60y)"  
		local lt5	"Only older people (>=60y)"  
		local lt6  "only children and adolescents (<=19y)"
		local lt7 "adults with at least one older adult (combination of t3 and t4)"
		foreach var of varlist $indicators_gmr25{
			   qui count if `var'_age==1&hhtype==`j'
			   if r(N)>15&~inlist(`var'_age,96,97){ // if more than 15 observations then produce the stats
			   * population average, std.err; upper & lower 95% CI bounds
				   qui ci proportions `var'_age [w=popweight] if hhtype==`j'
				   qui gen `var'_t`j'=r(mean)
				}
				else{ // if less than 15 observations  then do not produce the stat
				gen `var'_t`j'=98 if ~inlist(`var'_age,96,97)
				replace `var'_t`j'=`var'_age if inlist(`var'_age,96,97)
				}
			}
				qui sum t`j' [w=hhw] if ~missing(t`j')&t`j'~=99
				qui gen  hhw_t`j'=r(mean)
				label variable hhw_t`j' "% of hh in `lt`j''"
				qui sum t`j' [w=popweight] if ~missing(t`j')&t`j'~=99
				qui gen popw_t`j'=r(mean)
				label variable popw_t`j' "% of pop in `lt`j''"

		}
	}
	capture confirm variable t7
					if !_rc{
						count if t7~=99&~missing(t7)
						if r(N)~=0{
								foreach var of varlist $indicators_gmr25{
									count if `var'_age==1&t7==1
									if r(N)>15&~inlist(`var'_age,96,97){
									* population average, std.err; upper & lower 95% CI bounds
									   qui ci proportions `var'_age [w=popweight] if t7==1
									   qui gen `var'_t7=r(mean)


									}
								}
									qui sum t7 [w=hhw] if ~missing(t7)&t7~=99
									qui gen hhw_t7=r(mean)
									qui sum t7 [w=popweight] if ~missing(t7)&t7~=99
									qui gen popw_t7=r(mean)
									}
							}
							else{
							display "variable t7 is missing"
							}

}


* BY HEAD'S AGE


if inlist(flag_hh1age,99,98,96){
	forvalues i=0/1{
		foreach var of varlist $indicators_gmr25{
		 gen `var'_h`i'=flag_hh1age
		}
		qui gen hhw_h`i'=flag_hh1age
		qui gen popw_h`i'=flag_hh1age

	}
}
else{

	replace popweight=round(hhw*hhsize_ori,1)
	forvalues j=0/1{
		local lh0 "(head <60y)"
		local lh1 "(head >=60y)"
			count if ~missing(h`j')&h`j'~=99
			if r(N)~=0{
				foreach var of varlist $indicators_gmr25{
			   qui count if `var'_hh1age==1&h`j'==1
			   if r(N)>15&~inlist(`var'_hh1age,96,97){ // if more than 15 observations then produce the stats
			   * population average, std.err; upper & lower 95% CI bounds
				   qui ci proportions `var'_hh1age [w=popweight] if h`j'==1
				   qui gen `var'_h`j'=r(mean)

				}
				else{ // if less than 15 observations  then do not produce the stat
				gen `var'_h`j'=98 if ~inlist(`var'_hh1age,96,97)
				replace `var'_h`j'=`var'_hh1age if inlist(`var'_hh1age,96,97)
				}
			}
				qui sum h`j' [w=hhw] if ~missing(h`j')&h`j'~=99
				qui gen hhw_h`j'=r(mean)
				label variable hhw_h`j' "% of hhds with `lh`j''"
				qui sum h`j' [w=popweight] if ~missing(h`j')&h`j'~=99
				qui gen popw_h`j'=r(mean)
				label variable popw_h`j' "% of pop with `lh`j''"

		}
	}
}





* BY HEAD'S GENDER

if inlist(flag_hh1gender,99,98,96){
	forvalues i=0/1{
		foreach var of varlist $indicators_gmr25{
		 gen `var'_g`i'=flag_hh1gender
		}
		qui gen hhw_g`i'=flag_hh1gender
				qui gen popw_g`i'=flag_hh1gender
	}
}
else{

	replace popweight=round(hhw*hhsize_ori,1)
	forvalues j=0/1{
				local lg0 "(head female)"
		local lg1 "(head male)"
			count if ~missing(g`j')&g`j'~=99
			if r(N)~=0{
				foreach var of varlist $indicators_gmr25{
			   qui count if `var'_hh1gender==1&g`j'==1
			   if r(N)>15&~inlist(`var'_hh1gender,96,97){ // if more than 15 observations then produce the stats
			   * population average, std.err; upper & lower 95% CI bounds
				   qui ci proportions `var'_hh1gender [w=popweight] if g`j'==1
				   qui gen `var'_g`j'=r(mean)
				}
				else{ // if less than 15 observations  then do not produce the stat
				gen `var'_g`j'=98 if ~inlist(`var'_hh1gender,96,97)
				replace `var'_g`j'=`var'_hh1gender if inlist(`var'_hh1gender,96,97)
				}
			}
				qui sum g`j' [w=hhw] if ~missing(g`j')&g`j'~=99
				qui gen hhw_g`j'=r(mean)
				label variable hhw_g`j' "% of hhds with `lh`j''"

				qui sum g`j' [w=popweight] if ~missing(g`j')&g`j'~=99
				qui gen popw_g`j'=r(mean)
					label variable popw_g`j' "% of pop with `lh`j''"

		}
	}
}




* convert to percent 
 
			foreach num of numlist 96(1)99{
					for var  FHD_SPL100*h*  FHD_SPL40*h* FHD_SPL100*g* FHD_SPL40*g* FHD_SPL100*t*  FHD_SPL40*t*  : ///
					replace X=999`num' if inlist(X,`num')
					}
					
					for var FHD_SPL100*h*  FHD_SPL40*h* FHD_SPL100*g* FHD_SPL40*g* FHD_SPL100*t*  FHD_SPL40*t* : ///
					replace X=X*100  if ~inlist(X,99996,99997,99998,99999)
					
					for var hhw_t* popw_t* hhw_g* popw_g* hhw_h* popw_h*: replace X=99996 if inlist(X,96) 
					for var hhw_t* popw_t* hhw_g* popw_g* hhw_h* popw_h*: replace X=99997 if inlist(X,97) 
					for var hhw_t* popw_t* hhw_g* popw_g* hhw_h* popw_h*: replace X=99998 if inlist(X,98) 
					for var hhw_t* popw_t* hhw_g* popw_g* hhw_h* popw_h*: replace X=99999 if inlist(X,99) 

					for var hhw_t* popw_t* hhw_g* popw_g* hhw_h* popw_h*: replace X=X*100 if ~inlist(X,99996,99997,99998,99999)
	
* label variables: /* added 29 May 2025 */


	   
					   
			* age structure		   
		* revised SDG
		   	foreach i in 100 40{ 		 
			local l100 "OOP>100% of disc. CONS/INC"
			local l40 "OOP>40% of disc. CONS/INC"
			
			forvalues t=1/7{
			   local tl1	"Adults only: 20–59y" 
			   local tl2	"Younger hhds: families- all <59y"
			   local tl3	"Multigenerational hhds "  
			   local tl4	"Older hhds: >=20y & >59y"  
			   local tl5	"Only older hhds: >=60y"  
			   local tl6  "Very young hhds: <=19y"
			   local tl7 "Multigen & Older: t3&t4"
			   capture confirm variable FHD_SPL`i'_t`t'
				if !_rc{
			   label var FHD_SPL`i'_t`t' "(`tl`t'') `l`i''"
			   }
			   }	   
			   }

					   
			* head age   
			* revised SDG
		   	foreach i in 100 40{ 		 
			local l100 "OOP>100% of disc. CONS/INC"
			local l40 "OOP>40% of disc. CONS/INC"
				forvalues h=0/1{
			   local hl0	"head <60y" 
			   local hl1	"head >=60y"
			   			   capture confirm variable FHD_SPL`i'_h`h'
				if !_rc{
			   label var FHD_SPL`i'_h`h' "(`hl`h'') `l`i''"
			   }
				}
			}
					   
			* head sex   
					   	foreach i in 100 40{ 		 
			local l100 "OOP>100% of disc. CONS/INC"
			local l40 "OOP>40% of disc. CONS/INC"
				forvalues g=0/1{
			   local gl0	"head female" 
			   local gl1	"head male"
			   			   capture confirm variable FHD_SPL`i'_g`g'
				if !_rc{
			   label var FHD_SPL`i'_g`g' "(`gl`g'') `l`i''"
			   }
				}
			}



********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant) and Gabriela Flores (WHO staff).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO does not provide the data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/

