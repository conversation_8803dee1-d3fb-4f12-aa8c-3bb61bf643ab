****************************************************************************************************************************************
*Objective: Generate variables to identify the age of the household head, sex of the household head and the age composition of the household
*			perform quality checks
*********************************************************************************************************************************************
*[DO NOT MODIFY ANY CONTENT IN THIS DO FILE]


*************************************************************************************
* Version: May 24, 2025 
* Produced by WHO HQ-Financial protection monitoring team
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (FPCC_HHD typology.do)
*****************************************************************************************/

********************************************************************************************************************************************
* DEFINE CODES FOR NON-VALID CASES

label define nonvalid 99 "not found" 98 "less than 15obs" 96 "missing rate>10%" 97 "5%-10% missing rate & dif in indicator>1%",modify


*******************************************************************************************************************************************
* HHD SIZE FROM THE HHD LEVEL MODULE (AS GIVEN IN THE DATASET IN MOST CASES)
* REMEMBER THE VARIABLE HHSIZE_C CORRESPOND  TO THE HHSIZE CONSTRUCTED FROM THE INDIVIDUAL LEVEL MODULE
 
	capture confirm variable hhsize_ori
	if !_rc{
		 display in red "variable hhsize_ori already in dataset"
		 capture confirm variable hhsize
		 	if !_rc{
			display in red "variable hhsize also included in dataset"
					 cap drop hhsize
					 gen hhsize=hhsize_ori
					 }
					 else{
					display in red "variable hhsize NOT included in dataset"
					gen hhsize=hhsize_ori
					 }
	}
	else{
		capture confirm variable hhsize
		if !_rc{
		display in red "variable hhsize_ori missing but hhsize included"
		rename hhsize hhsize_ori
		label variable hhsize_ori "hhsize variable as included in the original dataset"
		}
		else{
			display in red "variable hhsize_ori AND hhsize var BOTH missing"
		}
	}
	
	
***************************************************************************************************************************************************************
*	CHECK AGE AND GENDER INFORMATION COMPLETENESS 

* AGE OF ALL HHD MEMBERS

capture confirm variable check_age
if !_rc{
	display in red "variable check_age INCLUDED"
	cap drop hhsize_age
	egen hhsize_age=rowtotal(chld ado adlt)
	cap drop check_age
	gen check_age=abs(hhsize_ori-hhsize_age)
	label variable hhsize_age "hhsize constructed as the sum of chld ado adlt var"
	label variable check_age "dif btw hhsize from HHD module & sum of chld ado adlt var"

	
	** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON AGE FOR MORE THAN 1 MEMBER
	cap drop check_ageid
	gen check_ageid=(check_age>1)		
	replace check_ageid=. if missing(check_age)
	
	** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5% & RATE >10%
	sum check_ageid					
	cap drop flag_age
	gen flag_age=0 if r(mean)<0.05
	replace flag_age=1 if r(mean)>=0.05&r(mean)<=0.1
	replace flag_age=96 if r(mean)>0.1
}
else{
	display in red "variable check_age missing"
	capture confirm variable adlt
		if !_rc{
			display in red "information on number of adults is available"
			cap drop hhsize_age
			egen hhsize_age=rowtotal(chld ado adlt)
			cap drop check_age
			gen check_age=abs(hhsize_ori-hhsize_age)
			label variable hhsize_age "hhsize constructed as the sum of chld ado adlt var"
			label variable check_age "dif btw hhsize from HHD module & sum of chld ado adlt var"

			
			** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON AGE FOR MORE THAN 1 MEMBER
			gen check_ageid=(check_age>1)		
			replace check_ageid=. if missing(check_age)
			
			** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5% & RATE >10%
			sum check_ageid					
			gen flag_age=0 if r(mean)<0.05
			replace flag_age=1 if r(mean)>=0.05&r(mean)<=0.1
			replace flag_age=96 if r(mean)>0.1
		}
		else{
			display in red "information on age structure of the hhd is not available"
			gen flag_age=99
			gen check_ageid=.
			}
}


* AGE & GENDER OF ALL HHD MEMBERS	*


capture confirm variable adlt_f 
 if !_rc{
	cap drop hhd_af
	egen hhd_af=rowtotal(chld_f ado_f adlt_f)
	cap drop hhd_am
	egen hhd_am=rowtotal(chld_m ado_m adlt_m)
	
	
	egen hhsize_agender=rowtotal(hhd_af hhd_am)
	gen check_agender=abs(hhsize_ori-hhsize_agender)
	label variable check_agender "dif btw hhsize & sum of hhd_f hhd_m var"
	label variable hhsize_agender "hhsize constructed as the sum of the number of female and male members"
	

	** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON GENDER
	gen check_agenderid=(check_agender>1)		
	replace check_agenderid=. if missing(check_agender)
	
	** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION FOR MORE THAN 1 MEMBER AND FLAG IF RATE>5%
	sum check_agenderid	
	cap drop flag_agender
	gen flag_agender=0 if r(mean)<0.05
	replace flag_agender=1 if r(mean)>=0.05&r(mean)<=0.1
	replace flag_agender=96 if r(mean)>0.1
}
else{
	capture confirm variable  hh_f
	 if !_rc{
		display in red "variable total # female already exists"
		capture confirm variable  hh_m
			if !_rc{
			display in red "variable total # male already exists"	
			rename hh_f hhd_af
			rename hh_m hhd_am
			egen hhsize_agender=rowtotal(hhd_af hhd_am)
			gen check_agender=abs(hhsize_ori-hhsize_agender)
			label variable check_agender "dif btw hhsize & sum of hhd_f hhd_m var"
			label variable hhsize_agender "hhsize constructed as the sum of the number of female and male members"
			

			** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON GENDER
			gen check_agenderid=(check_agender>1)		
			replace check_agenderid=. if missing(check_agender)
			
			** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5%
			sum check_agenderid
				cap drop flag_agender
			gen flag_agender=0 if r(mean)<0.05
			replace flag_agender=1 if r(mean)>=0.05&r(mean)<=0.1
			replace flag_agender=96 if r(mean)>0.1
			}
		else{
				display in red "variable total # female exists BUT # male is missing"
					cap drop flag_agender
				gen flag_agender=99
			}
		}
	else{
	display in red "variable on member's gender missing"
					cap drop flag_agender
	gen flag_agender=99
	}
}
	



* HEAD'S AGE

capture confirm variable hh1_aged
 if !_rc{
	display "HEAD'S AGE category is NOT missing"
	** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON HEAD'S AGE
	gen check_hh1ageid=(missing(hh1_aged))		
	
	** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5%
	sum check_hh1ageid					
	gen flag_hh1age=0 if r(mean)<0.05
	replace flag_hh1age=1 if r(mean)>=0.05&r(mean)<=0.1
	replace flag_hh1age=96 if r(mean)>0.1
}
else{
	display "HEAD'S AGE category is missing"
	capture confirm variable hh1_agey
	 if !_rc{
		display "HEAD'S AGE numerical variable is NOT missing"
		** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON HEAD'S AGE
		gen check_hh1ageid=(missing(hh1_agey))		
		
		** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5%
		sum check_hh1ageid					
		gen flag_hh1age=0 if r(mean)<0.05
		replace flag_hh1age=1 if r(mean)>=0.05&r(mean)<=0.1
		replace flag_hh1age=96 if r(mean)>0.1
	}
	else{
		display "HEAD'S AGE category AND HEAD'S AGE numerical variable ARE BOTH MISSING"
			gen flag_hh1age=99
		}
	}
	
	

* HEAD'S GENDER

capture confirm variable hh1_male
 if !_rc{

	** IDENTIFY THE NUMBER OF HHD WITH MISSING INFORMATION ON HEAD'S GENDER
	cap confirm variable hh1_female
	if !_rc{
		cap drop hh1_gender
	egen hh1_gender=rowtotal(hh1_male hh1_female)
	}
	else{
	gen hh1_female=1-hh1_male
		egen hh1_gender=rowtotal(hh1_male hh1_female)
	}
	cap drop check_hh1genderid
	gen check_hh1genderid=(missing(hh1_gender))		
	
	** IDENTIFY THE % OF THE SAMPLE WITH MISSING INFORMATION WITH MORE THAN 1 MEMBER AND FLAG IF RATE>5%
	sum check_hh1genderid		
	cap drop flag_hh1gender
	gen flag_hh1gender=0 if r(mean)<0.05
	replace flag_hh1gender=1 if r(mean)>=0.05&r(mean)<=0.1
	replace flag_hh1gender=96 if r(mean)>0.1
}
else{
	display in red "variable on head's gender missing"
	gen flag_hh1gender=99
}
	
	
	label variable flag_age "flag sample w/ age info missing for more than 1 hhd member"
	label variable flag_agender "flag sample w/ age& gender info missing for more than 1 hhd member"
	label variable flag_hh1age "flag sample w/ head's age info missing"
	label variable flag_hh1gender "flag sample w/ head's gender info missing"
	
	


***************************************************************************************************************************************************************
* HHD AGE COMPOSITION
 	
/*	AGE STRUCTURE OF THE HHD
	t1	Only adults aged 20-59 years old 
	t2	Adults 20-59y with young dependents (children and adolescents <=19 years old, could be further split into with or without adolescents – adolescence starts at age 10 according to WHO just as an FYI).   
	t3	Multigenerational households (adults – 20to59 years old, with both young dependents <=19y and old dependents >=60y)  
	t4	Adults 20-59y with old dependents (>=60y)  
	t5	Only older people (>=60y)  
	t6  only children and adolescents (<=19y)
	t7  adults with at laest one older adult (combination of t3 and t4)
*/



capture confirm variable check_age
if !_rc{
					 display in red "check_age variable is in the dataset"
					 	capture assert mi(check_age)
						if _rc==0 {
							disp "check_age in the dataset BUT with missing values"
							}
							else{
							disp "check_age in the dataset AND WITHOUT missing values"
						* identify each type of hhd
						gen t1 = (hhsize_age==adlt& adlto == 0 )  //  only adults 20-59y
						gen t2 = ((chld!=0|ado!=0) & adlt != 0 & adlto == 0&hhsize_age~=adlt  )   // adults 20-59y w/ children 
						gen t3 = ((chld!=0|ado!=0) & adlt != 0 & adlto != 0&hhsize_age~=adlt     )     // multigenerational hhds
						gen t4 = (adlto != 0&hhsize_age==adlt &hhsize_age~=adlto      )     // adults 20-59y w/older adults
						gen t5 = (hhsize_age==adlto& adlto ~= 0) // only older adults (>=60y)
						gen t6 = ((chld!=0|ado!=0) & adlt == 0 & hhsize_age~=adlt) // no adult (age<=19y)
						gen t7 = (t3==1|t4==1) // hhds with old depedents including multigenerational ones
						
						* replace by missing if there is no information or incomplete information

							forvalues i=1/7{
							replace t`i'=. if missing(hhsize_age)|check_ageid~=0|flag_age==99
							}

						* check categories are mutually exclusive

							egen check_t=rowtotal(t1 t2 t3 t4 t5 t6)
							label variable check_t "flag hhds with non-mutually exclusive groups"
							sum check_t
							if r(mean)~=1{  

								// if a hhd falls in two categories, remove from the analysis
								display in red "variables t1 to t6 ARE NOT mutually exclusive for at least one hhd"
									forvalues i=1/7{
									replace t`i'=. if check_t~=1  
									}
									
								// make sure now all hhds fall in only one category
									cap drop check_t
									egen check_t=rowtotal(t1 t2 t3 t4 t5 t6) 
									
								// mark hhds that have been removed because they fall in more than category		
								replace check_ageid=1 if missing(check_t)
						
								// reproduce the flag variable
									cap drop flag_age
									sum check_ageid					
									gen flag_age=0 if r(mean)<0.05
									replace flag_age=1 if r(mean)>=0.05&r(mean)<=0.1
									replace flag_age=96 if r(mean)>0.1
									
								// if the number of missing case is not problematic create one single variable 
								
									sum flag_age
									if r(mean)~=96{
											gen hhtype=t1
											forvalues i=2/6{
											replace hhtype=`i' if t`i'==1
											}
										}
										else{
											gen hhtype=.
										}

								// check the number of observations in the group t7
								count if t7==1
								if r(N)==0{
								display in red "there are NO adults with at laest one older adult (combination of t3 and t4)"
								replace t7=99
								}
								else{
									display in red "there ARE adults with at laest one older adult (combination of t3 and t4)"
									}
								}
								else{
									// if t var are mutually exclusive for all hhds
									display in red "variables t1 to t6 ARE mutually exclusive for all hhds"

									sum flag_age
										if r(mean)~=96{
												gen hhtype=t1
												forvalues i=2/6{
												replace hhtype=`i' if t`i'==1
												}
												replace hhtype=. if missing(hhsize_age)|check_ageid~=0|flag_age==99 
											}
											else{
												gen hhtype=99
											}					
			// check the number of observations in the group t7
			count if t7==1
			if r(N)==0{
			display in red "there are NO adults with at laest one older adult (combination of t3 and t4)"
			replace t7=99
			}
			else{
				display in red "there ARE adults with at laest one older adult (combination of t3 and t4)"
				replace t7=. if missing(hhsize_age)|check_ageid~=0
			}
		}
		label variable hhtype "age composition type of hhd"
			label define htc 1 "Only adults (20 to 59y)" ///
			2 "adults (20 to 59y) with children and/or adolescents" ///
			3 "multigenerational households" ///
			4 "adults with older people" ///
			5 "only older person (>=60y)" ///
			6 "adolescents w or w/o children", modify
			label values hhtype htc
			
		label variable t1	"Only adults aged 20-59 years old" 
		label variable t2	"Adults 20-59y with children and adolescents <=19y"
		label variable t3	"Multigenerational households"  
		label variable t4	"Adults 20-59y with old dependents (>=60y)"  
		label variable t5	"Only older people (>=60y)"  
		label variable t6  "only children and adolescents (<=19y)"
		label variable t7 "adults with at least one older adult (combination of t3 and t4)"

	}
}
else{
						gen hhtype=99
						/*forvalues i=1/7{
						gen t`i'=99
						}*/
}

		

***************************************************************************************************************************************************************
* HHD HEAD CHARACTERISTICS
 	

	
* head aged 60+
	
	sum flag_hh1age
	if r(mean)~=96{
	capture confirm variable hh1_age60d
		if !_rc{
			gen h1=(hh1_age60d==1&~missing(hh1_age60d))
			gen h0=(hh1_age60d==0&~missing(hh1_age60d))
			forvalues i=0/1{
			replace h`i'=. if missing(hh1_age60d)|check_hh1ageid~=0
			}
				label variable h0 "head is less than 60 years old"
			label variable h1 "head is 60 years old or older"
		}
		else{
			display "HEAD AGE CAT VARIABLE IS MISSING"
			capture confirm variable hh1_agey
			if !_rc{
				gen h1=(hh1_agey>=60&~missing(hh1_agey))
				gen h0=(hh1_agey<60&~missing(hh1_agey))
				forvalues i=0/1{
				replace h`i'=. if missing(hh1_agey)|check_hh1ageid~=0
					}
			label variable h0 "head is less than 60 years old"
			label variable h1 "head is 60 years old or older"
				}
				else{
				display "HEAD AGE NUM & CAT VARIABLE ARE BOTH MISSING"
				}
			}
	}
	else{
			forvalues i=0/1{
			cap gen h`i'=. if flag_hh1age==96
			}
	}




* female versus male head

	sum flag_hh1gender
	if r(mean)~=96&r(mean)~=97&r(mean)~=98&r(mean)~=99{
	capture confirm variable hh1_male
		if !_rc{
			display in red "variable hh1_male is NOT missing"
				gen g0=(hh1_male==0&~missing(hh1_male)) 
				gen g1=(hh1_male==1&~missing(hh1_male)) 
				forvalues i=0/1{
				replace g`i'=. if missing(hh1_male)|check_hh1genderid~=0
				}
		label variable g0 "head is female"
		label variable g1 "head is male"
		}
		else{
			display in red "variable hh1_male is missing"
			capture confirm variable hh1_female
			if !_rc{
						display in red "variable hh1_male is missing BUT h1_female is NOT"
							gen g0=(hh1_female==1&~missing(hh1_female)) 
							gen g1=(hh1_female==0&~missing(hh1_female)) 
							forvalues i=0/1{
							replace g`i'=. if missing(hh1_female)|check_hh1genderid~=0
							}
							label variable g0 "head is female"
							label variable g1 "head is male"
							}
							else{
							display in red "variable hh1_male AND h1_female ARE BOTH missing"
							}
		}
	}
	else{
		display in red "flag_hh1gender inlist 96, 97, 98, 99"
			forvalues i=0/1{
			gen g`i'=. if flag_hh1gender==96
			}
	}


	
* individual specific variables
capture confirm variable chld
if !_rc{
           gen chldweight  = chld*hhw
           gen adoweight   = ado*hhw
           gen adltweight  = adlt*hhw
           gen adltoweight = adlto *hhw
	}
	else{
		display in red "chld variable missing"
			
	}


********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant) and Gabriela Flores (WHO staff).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO does not provide the data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/
