/************************************************************************************
* Preparing data for SDG 3.8.2 Tracking Assessment calculation Tool  
***************************************************************************************
* Objective: 
* Identify key variables to produce SDG 3.8.2 indicator and its components using the TEST datasets included in the mock_rawdata folder.
* There is no need to make any changes but please read it to understand what needs to be done to prepare the key variables described in the "data requirements" document.

*-----------------------------------------------------------------------------------------------------------
* Code structure
*-----------------------------------------------------------------------------------------------------------
* [WARNING] IF YOUR DATASET ALREADY INCLUDES THE VARIABLES INDICATED IN "WHOFP-STATS - data requirements - May 2025.doc" WITH THE SAME VARIABLE NAME,
* GO STRAIGHT TO THE SECTION MERGE WITH PURCHASING POWER PARITY
*-----------------------------------------------------------------------------------------------------------

* INSTALL ADDITIONAL COMMANDS - DEPENDS ON YOUR STATA VERSION - NOT REQUIRED IF ALREADY DONE IN THE MASTER
* HOUSEHOLD TOTAL OUT-OF-POCKET HEALTH EXPENDITURE 
* HOUSEHOLD TOTAL CONSUMPTION EXPENDITURE
* ADDITIONAL  VARIABLES FROM HHD LEVEL DATASET: COMPULSORY: Household size, household weight / OPTIONAL: rural/urban place of residence
* ADDITIONAL  VARIABLES FROM INDIVIDUAL LEVEL DATASET TO OBTAIN INFORMATION ABOUT THE HHD COMPOSITION: * OPTIONAL: age of household's head, sex of hhd's head, age composition of the household
* MERGE OUT-OF-POCKET / TOTAL CONSUMPTION / HHD LEVEL / HHD COMPOSITION DATASETS  
* MERGE WITH PURCHASING POWER PARITY (PPP) DATASET PROVIDED BY WHO  
* SAVE THE DATASET FOR USE WITH THE WHO FINANCIAL PROTECTION -SDG 3.8.2 TRACKING ASSESSMENT ONLINE TOOL 
* DELETE INTERMEDIARY FILES 
* ADDITIONAL VARIABLES REQUIRED FOR COMPUTATIONS WITH THE WHO STATA PACKAGE



* Version: May 29, 2025 
* Produced by WHO HQ-Financial protection monitoring team
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (01_Prepare Do file)
*****************************************************************************************/


**********************************************************************************************************************
* Install additional commands - depends on your STATA version
**********************************************************************************************************************
*Note: This section is deactivated by default
*      You can active this section by deleting "/*" and "*/" in this subsection below

/*
ssc install missings, replace   /* Stata version 9 or up is required */
ssc install tabstatmat, replace /* Stata version 8 or up is required */
ssc install distinct, replace   /* Stata version 8 or up is required */
search gr0034    /*a window will pop up, select install gr0034 Stata version 7 or up is required */
*/


**********************************************************************************************************************
** HOUSEHOLD (HHD) TOTAL OUT-OF-POCKET HEALTH EXPENDITURE ** 
**********************************************************************************************************************
** The dataset with variables related to household out-of-pocket health expenditure 
** should consist on one observation per household for a given time reference (day; week, month; year )  at the household (hhd) level, i.e.
** if you need support to produce such household level dataset, please contact us and be ready to share your survey questionnaire as well as a data dictionary.
**********************************************************************************************************************

*---------------------------------------------------------------------------------------------------------
* OPEN THE DATASET AND IDENTIFY HHD TOTAL OOP HEALTH EXPENDITURES [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------

	use "$rawdata\TEST_YYYY_SRVA_HEALTHEXP.dta",clear  // Change the name of the test dataset ("TEST_YYYY_SRVA_HEALTHEXP.dta") to your file name 
	
	gen hexp=oop2           	 //HHD total OOP health expenditure-Specify the variable name used in the appropriate data 
	
*---------------------------------------------------------------------------------------------------------
* TYPES OF SPENDING [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------
*Specify variables below used in analysis to align with appropriate data
*Replace "." with corresponding variable from your dataset or formula to calculate the value
*If the data for this expenditure category is missing, do not replace "."

	gen oop_drug=oop2_med 	    /*HHD total OOP expenditure on medicines and pharmaceutical preparations, includes: */
						/* expenditure on medicines (branded, generic, herbal, homeopathic) */
						/*vaccines, oral contraceptives, and other pharmaceutical preparations intended for consumption or use outside a health facility or institution */
						/* If the survey follows COICOP 2018, this category corresponds to code 06.1.1.*/

	
	gen oop_hlthp=oop2_prod    /*HHD total OOP expenditure on health products, includes: */
					   /* medical products (e.g., antigen tests, glucose meters, masks) */
					   /* assistive health products for vision (e.g., glasses), hearing (e.g., hearing aids), and */
					   /* mobility (e.g., crutches, therapeutic footwear) including repair, rental, and online purchases.*/
					   /* If the survey follows COICOP 2018, this category corresponds to codes 06.1.2, 06.1.3, and 06.1.4.  */
					   	
	gen oop_dent=oop2_dent 	   /* HHD total OOP expenditure on outpatient dental services. If the survey follows COICOP 2018, this category corresponds to code 06.2.2 */

	gen oop_othoutp=oop2_medserv  /* HHD total OOP expenditure on immunization/vaccinations services, */
					   /* other preventive services (e.g., tetanus toxoid for pregnant women, and routine immunization such as BCG during well-child visits) */
					   /* and other medical outpatient services.*/
					   /* If the survey follows COICOP 2018, this category corresponds to code 06.2.1, 06.2.3 */

	gen oop_diag=oop2_diag 	   /* HHD total OOP expenditures on Diagnostic imaging services and medical laboratory services, such as blood tests and x-rays.*/
					   /* If the survey follows COICOP 2018, this category corresponds to code 06.4.1 */
					   
	gen oop_emerg=.    /* HHD total OOP expenditures on emergency transportation and emergency rescue services*/
					   /* If the survey follows COICOP 2018, this category corresponds to code 06.4.2 */
					   
	gen oop_inp=oop2_hosp      /* HHD total OOP expenditure on inpatient dental and medical services */
					   /* If the survey follows COICOP 2018, this category corresponds to code 06.3 */
					   
	gen oop_nec=. 		/* HHD total OOP expenditures on other products and services not elsewhere classified */
						/* this type of spending EXCLUDEs non-emergecy transportation and health insurance premiums */

*----------------------------------------------------------------------------------------------------------------------------------
* REGROUP SOME CATEGORIES OF OOP HEALTH EXPENDITURE INTO BROADER CATEGORIES FOR CROSS-COUNTRY COMPARABILITY [DO NOT MODIFY THIS SECTION]
*----------------------------------------------------------------------------------------------------------------------------------		

* regroup spending on all services provided without overnight stay 
	egen oop_outp=rowtotal(oop_dent oop_othoutp oop_diag oop_emerg)
	
*---------------------------------------------------------------------------------------------------------
* SPECIFY THE REFERENCE PERIOD OF THE OOP HEALT EXPENDITURE  VARIABLE AND ITS COMPONENTS [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------
* REFERENCE PERIOD CODE
	* 0 = recall period is a day; 
	* 1 = recall period is a week, 
	* 2 = recall period is a month; 
	* 3 = recall period is year. 

gen standard_oop_pds=2       //Replace "." with value 0 to 3 (see specification above) 

*---------------------------------------------------------------------------------------------------------
* SPECIFY THE HHID VARIABLE, COUNTRY ISO3C, YEAR [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------

** Identify the hhd variable and CHECK how many observations and households are included in the dataset
	gen HHID =hhid_str		        //Replace "." with the name of the variable that specifies household ID in your dataset
	distinct HHID  	 		//count nb of observations and households. Pre-condition: install the command distinct
	
** Specify the three-letter ISO code 
	gen iso3c="ISO" 			//Replace "ISO" with the three-letter ISO code of your country or territory
					
** Specify the (first) year of the survey
	gen year=2009 			//Replace "2009" with the survey year using 4 digits (e.g. 2025). If the data collection spans two years, select the year it began (e.g. 2024 if 2024-2025)
					    //If there are multiple rounds of data, select the year that uniquely identifies the round
					
 
*--------------------------------------------------------------------------------------------------------------------------------------------------
* REPLACE ALL MISSING VALUES BY ZERO AND CHECK IF THE SOME OF OOP COMPONENTS IS EQUAL TO THE TOTAL OOP HEALTH EXPENDITURE [DO NOT MODIFY THIS SECTION]
*--------------------------------------------------------------------------------------------------------------------------------------------------	
* replace all missing values by zero for households not reporting any health consumption expenditure
	foreach var of varlist hexp oop* { 	/* the "*" means all the variable that start with "oop2" are part of the variable list */
	  		  replace `var'=0 if missing(`var')
  			}
* sum of types of spending
	egen oop=rowtotal(oop_drug oop_hlth oop_outp oop_inp oop_nec)
	
* check if the sum of oop components is equal to the total oop health expenditure

	gen flag_oop=1 if oop~=hexp
	replace flag_oop=0 if oop==hexp
	if flag_oop==0{
		drop oop flag_oop
	}
	else {
		display "sum of OOP components is not equal to total OOP"
		lab var flag_oop "Binary: 1 means sum of OOP is not equal to total OOP/ 0 otherwise"
	}
	
	
*--------------------------------------------------------------------------------------------------------------------------------------------------
* CONVERT THE OOP HEALTH EXPENDITURE VARIABLE AND ITS COMPONENTS TO DAILY VALUES [DO NOT MODIFY THIS SECTION]
*--------------------------------------------------------------------------------------------------------------------------------------------------	
*Create a multiplier variable to convert monetary amounts to daily values
			gen recall_multiplier = .
			replace recall_multiplier = 1           if standard_oop_pds == 0   // daily
			replace recall_multiplier = 1/7         if standard_oop_pds == 1   // weekly
			replace recall_multiplier = 12/365      if standard_oop_pds == 2   // monthly
			replace recall_multiplier = 1/365       if standard_oop_pds == 3   // annual
			replace recall_multiplier = . if missing(standard_oop_pds)

* calculate  per day values per household
		   foreach var of varlist hexp oop* {
		   replace `var' = `var' * recall_multiplier
		   }
		   
		   
*--------------------------------------------------------------------------------------------------------------------------------------------------
* LABEL VARIABLES, DEFINE MACROS AND SAVE THE DATASET [DO NOT MODIFY THIS SECTION]
*--------------------------------------------------------------------------------------------------------------------------------------------------		
	
* labels
	lab var hexp "(total) OOP HEALTH EXPENDITURE (LCU/HHD/DAY)"
	lab var oop_drug "(medicines) OOPs (LCU/HHD/DAY) "					
	lab var oop_hlthp  " (health products) OOPs (LCU/HHD/DAY)"
	lab var oop_dent  " (dental outpatient services) OOPs (LCU/HHD/DAY)"
	lab var oop_othoutp "(preventive and other outpatient medical services than dental) OOPs (LCU/HHD/DAY)"
	lab var oop_diag "(Diagnostic and medical laboratory services) OOPs (LCU/HHD/DAY)"
	lab var oop_emerg "(Emergecy transportation and rescue) OOPs (LCU/HHD/DAY)"
	lab var oop_outp "(all services provided without overnight stay) OOPs (LCU/HHD/DAY)"
	lab var oop_inp	"(medical and dental care with overnight stay) OOPs (LCU/HHD/DAY)"
	lab var oop_nec "(not elsewhere classified) OOPs (LCU/HHD/DAY)"
	lab var standard_oop_pds "Reference period of OOP HEALTH EXPENDITURE variables"
	lab var recall_multiplier "multiplier to convert monetary amounts to daily value"

* macros
	glo iso3c=iso3c
	glo year=year
	
*save
   capture confirm variable flag_oop
    if !_rc {
	keep HHID hexp oop_drug oop_outp oop_hlthp oop_dent oop_othoutp oop_diag oop_emerg oop_inp oop_nec standard_oop_pds iso3c year recall_multiplier flag_oop
	}
	else {
	keep HHID hexp oop_drug oop_outp oop_hlthp oop_dent oop_othoutp oop_diag oop_emerg oop_inp oop_nec standard_oop_pds iso3c year recall_multiplier
	}
	
saveold "$wrkdata/${iso3c}_${year}_HEALTHEXP.dta",version(12) replace			


		
**********************************************************************************************************************
*** HOUSEHOLD (NON-FOOD AND FOOD) TOTAL CONSUMPTION 
**********************************************************************************************************************
*IMPORTANT: All values in this dataset should already be aggregated at the household level 
** 	        All values should be in nominal values
***************************************************************************************************

*---------------------------------------------------------------------------------------------------------
* OPEN THE DATASET & SPECIFY CONSUMPTION EXPENDITURE VARIABLES  [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------	

*OPEN DATASET (which contains the consumption expenditure variables)
use "$rawdata\TEST_YYYY_SRVA_CONSEXP.dta",clear // Change the name of the test dataset ("TEST_YYYY_SRVA_CONSEXP.dta") to your file name 

**CHANGE variables below used in analysis to align with appropriate data 
***Replace "." with corresponding variable from your dataset or formula to calculate the value
	gen exp=exp2	   //Replace "." with the variable name that specifies total household CONSUMPTION EXPENDITURE (preferred)/ household total expenditure (2nd best)
	gen fexp=cons_food   //Replace "." with the variable name that specifiestotal household food CONSUMPTION EXPENDITURE
	gen nfexp=cons_nfood  //Replace "." with the variable name that specifies total household  non-food CONSUMPTION EXPENDITURE 

*---------------------------------------------------------------------------------------------------------
* SPECIFY THE REFERENCE PERIOD OF THE CONSUMPTION EXPENDITURE  VARIABLE AND ITS COMPONENTS [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------
* REFERENCE PERIOD CODE
	* 0 = recall period is a day; 
	* 1 = recall period is a week, 
	* 2 = recall period is a month; 
	* 3 = recall period is year. 

gen standard_exp_pds=2       //Replace "." with value 0 to 3 (see specification above) 

*---------------------------------------------------------------------------------------------------------
* SPECIFY THE HHID VARIABLE, COUNTRY ISO3C, YEAR [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------

** Identify the hhd variable and CHECK how many observations and households are included in the dataset
	gen HHID =hhid_str		        //Replace "." with the name of the variable that specifies household ID in your dataset
	distinct HHID  	 		//count nb of observations and households. Pre-condition: install the command distinct
	
** Specify the three-letter ISO code 
	gen iso3c="ISO" 			//Replace "ISO" with the three-letter ISO code of your country or territory
					
** Specify the (first) year of the survey
	gen year=2009 			//Replace "2009" with the survey year using 4 digits (e.g. 2025). If the data collection spans two years, select the year it began (e.g. 2024 if 2024-2025)
					    //If there are multiple rounds of data, select the year that uniquely identifies the round
					
				
*---------------------------------------------------------------------------------------------------------
* REPLACE ALL MISSING VALUES BY ZERO AND QUALITY CHECKS [DO NOT MODIFY THIS SECTION]
*---------------------------------------------------------------------------------------------------------
* replace all missing values by zero for households not reporting any health consumption expenditure
	foreach var of varlist exp fexp nfexp { 	/* the "*" means all the variable that start with "oop2" are part of the variable list */
	  		  replace `var'=0 if missing(`var')
  			}
* sum of types of spending
	egen cons=rowtotal(fexp nfexp) 
	
* check if the sum of oop components is equal to the total oop health expenditure
	gen flag_cons=1 if cons~=exp
	replace flag_cons=0 if cons==exp
	if flag_cons==0{
		drop cons flag_cons
	}
	else {
		display "sum of cons components is not equal to total cons"
		lab var flag_cons "Binary: 1 means sum of cons is not equal to total cons/ 0 otherwise"
	}

*--------------------------------------------------------------------------------------------------------------------------------------------------
* CONVERT THE CONSUMPTION EXPENDITURE VARIABLE AND ITS COMPONENTS TO DAILY VALUES [DO NOT MODIFY THIS SECTION]
*--------------------------------------------------------------------------------------------------------------------------------------------------	
*Create a multiplier variable to convert monetary amounts to daily values
			gen recall_multiplier = .
			replace recall_multiplier = 1           if standard_exp_pds == 0   // daily
			replace recall_multiplier = 1/7         if standard_exp_pds == 1   // weekly
			replace recall_multiplier = 12/365      if standard_exp_pds == 2   // monthly
			replace recall_multiplier = 1/365       if standard_exp_pds == 3   // annual
			replace recall_multiplier = . if missing(standard_exp_pds)

* calculate  per day values per household
		   foreach var of varlist exp fexp nfexp {
		   replace `var' = `var' * recall_multiplier
		   }
		   
*--------------------------------------------------------------------------------------------------------------------------------------------------
* LABEL VARIABLES, DEFINE MACROS AND SAVE THE DATASET [MAY REQUIRE CHANGES]
*--------------------------------------------------------------------------------------------------------------------------------------------------		
	
* labels
	lab variable exp "household total consumption expenditure (LCU/HHD/DAY)"  /* delete "consumption" if the variable identifies only expenditures */
	lab variable fexp   "(food & non-alcoholic) consumption exp (LCU/HHD/DAY)"  /* delete "consumption" if the variable identifies only expenditures */
	lab variable nfexp "(non-food) consumption exp (LCU/HHD/DAY)" /* delete "consumption" if the variable identifies only expenditures */
    lab variable standard_exp_pds "Reference period of OOP EXPENDITURE variables"
    

* macros
	glo iso3c=iso3c
	glo year=year
	
*save
   capture confirm variable flag_cons
    if !_rc {
	keep HHID exp fexp nfexp cons flag_cons standard_exp_pds iso3c year recall_multiplier 
	}
	else {
	keep HHID exp fexp nfexp standard_exp_pds iso3c year recall_multiplier 
	}
	
saveold "$wrkdata/${iso3c}_${year}_EXP.dta",version(12) replace
		
					
***************************************************************************************************
*** ADDITIONAL  VARIABLES FROM HHD LEVEL DATASET
***************************************************************************************************
*IMPORTANT: COMPULSORY:  Household size, household weight 
** 	          OPTIONAL:  Rural/urban place of residence, other grouping of interest to the country
***************************************************************************************************


*OPEN DATASET AT HHD LEVEL WITH INFORMATION ABOUT HHD CHARACTERISTICS (HHSIZE, PLACE OF RESIDENCE ETC...)
use "$rawdata\TEST_YYYY_SRVA_HHD.dta",clear // Change the name of the test dataset ("TEST_YYYY_SRVA_HHD.dta") to your file name 


*Replace "." with corresponding variable from your dataset [REQUIRES MANUAL INPUTS]
	gen hhw=weight          //Replace "." with the variable name that specifies household weight
	gen HHID=hhid_str         //Replace "." with the variable name that specifies household ID
	gen hhsize=hhsize2       //Replace "." with the variable name that specifies household size  
	*gen urban=        //Replace "." with the variable name that specifies household urbanicity/rurality, where urban is 1 and rural is 0
					   // For surveys using a 'degree of urbanization' classification, cities, towns, and semi-dense areas are classified as urban. 
*---------------------------------------------------------------------------------------------------------
* SPECIFY THE COUNTRY ISO3C, YEAR [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------

** Specify the three-letter ISO code 
	gen iso3c="ISO" 			//Replace "ISO" with the three-letter ISO code of your country or territory
					
** Specify the (first) year of the survey
	gen year=2009 			//Replace "2009" with the survey year using 4 digits (e.g. 2025). If the data collection spans two years, select the year it began (e.g. 2024 if 2024-2025)
					    //If there are multiple rounds of data, select the year that uniquely identifies the round
				
					   
saveold "$wrkdata/${iso3c}_${year}_HHDINFO.dta",version(12) replace	


***************************************************************************************************
*** ADDITIONAL  VARIABLES FROM INDIVIDUAL LEVEL DATASET FOR DISAGGREGATION OF SDG 3.8.2 AND RELATED
***************************************************************************************************

*OPEN DATASET with household members information, usually at the individual level
use "$rawdata\TEST_YYYY_SRVA_HHMEMBERS.dta",clear // Change the name of the test dataset ("TEST_YYYY_SRVA_HHMEMBERS.dta") to your file name 

*------------------------------------------------------------------------------------------------------------------------
*  Create optional variables related to age of household's head, sex of hhd's head, and age composition of the household  [REQUIRES manual inputs]
*------------------------------------------------------------------------------------------------------------------------
**CHANGE variables below used in analysis to align with appropriate data 
***Replace "." with corresponding variable from your dataset
***If the data for this expenditure category is missing, do not replace "."
	gen SEX= SEX2				//Replace "." with the variable name that specifies sex of hhd member (where male is 1 and female is 2)
	gen AGE= AGE2              //Replace "." with the variable name that specifies age of hhd member (where age is numeric, 0 to 100)
	*gen AGEC=.				//Replace "." with the variable name that specifies age of hhd member (where age is a categorical variable) with the following categories:
							// 1 [<9 years]; 2 [10-14 years]; 3 [15-19 years] ; 4 [20-24 years] ; 5 [25-29 years] ; 6 [30-34 years] ; 7 [35-39 years] 
							// 8 [40-44 years] ; 9 [45-49 years]; 10 [50-54 years]; 11 [55-59 years] ; 12 [60-64 years] ; 13 [65-69 years] ; 14 [70-74 years] 
							// 15 [75-79 years] ; 16 [80-84 years]; 17 [ >= 85 ]. WARNING THE CUT-OFF VALUE OF 60 YEARS IS IMPORTANT. IT MUST CORRESPOND TO NUMBER 13
							// COMMENT OUT "gen AGED=." IF A CATEGORICAL AGE VARIABLE IS NOT INCLUDED IN THE DATASET
	gen RELHEAD=RELHEAD2 			//Replace "." with the variable name that identifies a person is head of the household (where hhd head is 1, not hhd head is 0)
	gen HHID=hhid_str              //Replace "." with the variable name that specifies household ID 
	
*---------------------------------------------------------------------------------------------------------
* SPECIFY THE COUNTRY ISO3C, YEAR [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------

** Specify the three-letter ISO code 
	gen iso3c="ISO" 			//Replace "ISO" with the three-letter ISO code of your country or territory
					
** Specify the (first) year of the survey
	gen year=2009 			//Replace "2009" with the survey year using 4 digits (e.g. 2025). If the data collection spans two years, select the year it began (e.g. 2024 if 2024-2025)
					    //If there are multiple rounds of data, select the year that uniquely identifies the round
				
*---------------------------------------------------------------------------------------------------------
*  Creating standartised variables  [DO NOT MODIFY THIS SECTION]
*---------------------------------------------------------------------------------------------------------		
* IDENTIFY HOUSEHOLD'S HEAD
	gen i_headhh=1 if RELHEAD==1
	replace i_headhh=0 if missing(i_headhh)&~missing(RELHEAD)
* IDENTIFY SEX of HOUSEHOLD'S MEMBER (male)
	gen i_male=(SEX==1)
	replace i_male=0 if SEX==2
* IDENTIFY SEX OF HOUSEHOLD'S HEAD 
	gen hh1_male=i_male if i_headhh==1
	replace hh1_male=0 if missing(hh1_male)&~missing(i_headhh)&~missing(i_male)
* IDENTIFY AGE OF HOUSEHOLD'S HEAD 
	capture confirm variable AGE
	if !_rc{
		display in green "(numerical) AGE variable included in the dataset"
		gen hh1_agey=AGE if i_headhh==1	
* IDENTIFY HOUSEHOLD'S HEAD THAT IS 60 YEAR AND OLDER: 
		gen hh1_age60d= (hh1_agey>=60&~missing(hh1_agey))
	}
	else{
				display in red "(numerical) AGE variable NOT included in the dataset"
	}

	capture confirm variable AGEC
	if !_rc{
			display in green "(categorical) AGE variable included in the dataset"
			gen hh1_agec=AGEC if i_headhh==1	
* IDENTIFY HOUSEHOLD'S HEAD THAT IS 60 YEAR AND OLDER: 
			gen hh1_age60d= (hh1_agec>=13&~missing(hh1_agec))
					}	
	else{
				display in red "(categorical) AGE variable NOT included in the dataset"
	}

	
* CLASSIFY HOUSEHOLD'S MEMBERS IN DIFFERENT AGE CATEGORIES (THESE VARIABLES ARE NEEDED TO CONSTRUCT HOUSEHOLD AGE STRUCTURE TYPOLOGY)
	gen chldu5=1 if AGE<5&~missing(AGE)
	gen chldo=1 if AGE>=5&AGE<=9
	gen chld=1 if AGE<=9
	gen adoy=1 if AGE>=10&AGE<=14
	gen adoo=1 if AGE>=15&AGE<=19
	gen ado=1 if AGE>=10&AGE<=19
	gen adlty=1 if AGE>=20&AGE<=24
	gen aged2529=1 if AGE>=25&AGE<=29
	gen aged3034=1 if AGE>=30&AGE<=34
	gen aged3539=1 if AGE>=35&AGE<=39
	gen aged4044=1 if AGE>=40&AGE<=44
	gen aged4549=1 if AGE>=45&AGE<=49
	gen adltr=1 if AGE>=15&AGE<=49
	gen aged5054=1 if AGE>=50&AGE<=54
	gen aged5559=1 if AGE>=55&AGE<=59
	gen aged6064=1 if AGE>=60&AGE<=64
	gen aged6569=1 if AGE>=65&AGE<=69
	gen aged7074=1 if AGE>=70&AGE<=74
	gen aged7579=1 if AGE>=75&AGE<=79
	gen aged8084=1 if AGE>=80&AGE<=84
	gen aged85=1 if AGE>=85&~missing(AGE)
	gen adlt=1 if AGE>=20&~missing(AGE)
	gen adlto=1 if AGE>=60&~missing(AGE)
* BINARY VARIABLE TO ASSESS THE COMPLETENESS OF THE AGE INFORMATION WITHIN EACH HHD
	gen hhsize_c=1 // This variable is produced to construct a household size variable when collapsing by HHID	
* COLLAPSING VARIABLES AT INDIVIDUAL LEVEL TO GET VALUES AT HHD LEVEL
	collapse (sum)  hhsize_c ado* chld* adlt* aged2529* aged3034* aged3539* aged4044* aged4549* aged5054* aged5559* aged6064* aged6569* aged7074* aged7579* aged8084* aged85* i_male (max) hh1_*, by(HHID)
* ADD LABELS TO STANDARTISED VARIABLES
	#delimit ;
	label variable hhsize_c "constructed household size";
	label variable chldu5 "nb of (hhd) Children between [0;59 months] or [0;4 years]"; 
	label variable chldo "nb of (hhd) Children between 5 and 9 years old"; 
	label variable chld "nb of (hhd) Children between 0 and 9 years old";
	label variable adoy "nb of (hhd) Children between [10;14] years old";
	label variable adoo "nb of older adolescents aged [15;19]";
	label variable ado "nb of adolescents aged [10;19]=sum adoy & adoo"; 
	label variable adlty "nb of young adults aged [20;24]";
	label variable aged2529 "nb of household members aged [25;29]";
	label variable aged3034 "nb of household members aged [30;34]";
	label variable aged3539 "nb of household members aged [35;39]";
	label variable aged4044 "nb of household members aged [40;44]";
	label variable aged4549 "nb of household members aged [45;49]";
	label variable aged5054 "nb of household members aged [50;54]";
	label variable aged5559 "nb of household members aged [55;59]";
	label variable aged6064 "nb of household members aged [60;64]";
	label variable aged6569 "nb of household members aged [65;69]";
	label variable aged7074 "nb of household members aged [70;74]";
	label variable aged7579 "nb of household members aged [75;79]";
	label variable aged8084 "nb of household members aged [80;84]";
	label variable aged85 "nb of household members aged 85 years or more (>85)";
	label variable adlt "total nb of household members aged 20 years old or more (>=20)";
	label variable adlto "total nb of household members aged 60 or more (>=60)";
	#delimit cr
		
	rename i_male hh_m
	label variable hh_m "total number of male in the household"
	label variable hh1_agey "(head) age  (years)"
	label variable hh1_male "(head) is male"
	label variable hh1_age60d "(head) is at least 60 years old"
		
*SAVE DATASET
saveold "$wrkdata/${iso3c}_${year}_HHDCOMP.dta",version(11) replace	


***************************************************************************************
** MERGE ALL DATASETS** 
***************************************************************************************
*---------------------------------------------------------------------------------------------------------
* [MAY REQUIRE MANUAL INPUTS IF ONE OF THE FILES LISTED HEREAFTER WAS NOT CREATED]
*			  ${iso3c}_${year}_HEALTHEXP.dta
*			  ${iso3c}_${year}_EXP.dta
*			  ${iso3c}_${year}_HHDINFO.dta
*             ${iso3c}_${year}_HHDCOMP.dta
*---------------------------------------------------------------------------------------------------------


	use "$wrkdata/${iso3c}_${year}_HEALTHEXP.dta",clear
	merge 1:1 HHID using "$wrkdata/${iso3c}_${year}_EXP.dta",update
	tab _merge 
	keep if _merge==3 /* keep only matched observations */
	drop _merge       /* as per the above, this variable can be dropped */
	  
	merge 1:1 HHID using "$wrkdata/${iso3c}_${year}_HHDINFO.dta",update
	keep if _merge==3 /* keep only matched observations */
	drop _merge       /* as per the above, this variable can be dropped */	
	merge 1:1 HHID using "$wrkdata/${iso3c}_${year}_HHDCOMP.dta",update
	keep if _merge==3 /* keep only matched observations */
	drop _merge       /* as per the above, this variable can be dropped */
	

*---------------------------------------------------------------------------------------------------------
* MERGE WITH THE PURCHASING POWER PARITY (PPP) DATASET [REQUIRES MANUAL INPUTS]
*---------------------------------------------------------------------------------------------------------	

**Do not forget to change the name of the .dta file below ("PPPfactors20241012_TEST.dta") to your file name that contains the PPP data.
merge m:1 iso3c year using "$wrkdata\PPPfactors20241012_TEST.dta",update
* drop irrelavant poverty lines' values
	drop if _merge~=3
	drop _merge
	rename SOURCE SOURCE_PPP
	rename  PL215 PL_215      
	
*---------------------------------------------------------------------------------------------------------
*SPECIFY OVERALL REFERENCE PERIOD [MAY REQUIRE MANUAL INPUTS]
*
* remember:  0 = recall period is a day; * 1 = recall period is a week, * 2 = recall period is a month; * 3 = recall period is year. 
*---------------------------------------------------------------------------------------------------------


gen standard_pds=0 // by default, the zero code is used as values have already been converted to daily amounts in previous sections.
				   // replace 0 by another value if the recommendations in previous sections were not followed
				   
* [DO NOT MODIFY THE FOLLOWING CODE]
capture confirm variable  recall_multiplier
    if !_rc {
		display "variable  recall_multiplier already included in the dataset"
	}
	else{
		gen recall_multiplier = .
	}

	lab variable standard_pds "Reference period overall"
    lab variable recall_multiplier "Reference period multiplier based on recall period"
	
*SAVE DATASET
saveold "$wrkdata/${iso3c}_${year}_v0s.dta",version(12) replace
	
*---------------------------------------------------------------------------------------------------------
* DELETE INTERMEDIARY FILES TO REDUCE DIGITAL WASTE  [DO NOT MODIFY THIS SECTION]
*---------------------------------------------------------------------------------------------------------		

erase "$wrkdata/${iso3c}_${year}_HEALTHEXP.dta"
erase "$wrkdata/${iso3c}_${year}_EXP.dta"
erase "$wrkdata/${iso3c}_${year}_HHDINFO.dta"
erase "$wrkdata/${iso3c}_${year}_HHDCOMP.dta"


********************************************************************************
** ADDITIONAL VARIABLES REQUIRED FOR COMPUTATIONS WITH THE WHO STATA PACKAGE  
********************************************************************************

* CREATE COUNTY NAME
	cap drop country 		//Deletes variables country if it already exists if your dataset
	gen cname = "Test"      //Change the country name inside the quotes by replacing the word Test inside quotes (e.g."Nepal")

* SPECIFY SURVEY ABBRIATION
	gen survey="."   //Specify acronym of survey used by replacing . inside quotes.
					 //For example, LSMS = Living Standards Measurement Survey, CWIQ = Core Welfare Indicators Questionnaire, HBS = Household Budget Survey. This is an optional field. 

* SPECITY SURVEY DATA VERSION
	gen vdata="v01_M"  //Specify the version of data, by default v01 is assumed. Replace 01 inside the quotes otherwise (e.g. "v03_M") 

* CREATE SURVEY REFERENCE ID 
**Specify reference id variable, using the format ISO3C_YEAR_SURVEYACRONYM_VERSION_ADAPTATION, where
***		ISO3c is the iso3c code of the country ; YEAR is the survery year (e.g.2025); SURVEY ACRONYM is the survey acronym (e.g HBS); 
*** 	VERSION is survey version; ADAPTATION is the survey adaptation

	gen referenceid= "ISO_YYYY_HBS_v01_M" //Specify survery reference (see explanation above)

* CREATE MONETARY WELFARE INDICATOR
** Specify welfare indicator 
*** If the monetary welfare indicator is different, replace it with income, wealth, expenditure, as appropriate, inside the quotes 
	gen welfare = "Consumption"   //Specify welfare indicator, change as appropriate

* CREATE POPULATION WEIGHTS (NO CHANGES REQUIRED, BUT PLEASE MAKE SURE THESE VARIABLES EXIST)
	gen popweight=hhw*hhsize
					
* CHECK THAT YEAR IS SPECIFIED
	cap sum year 		 //If this variable is missing, please create it 

* GENERATE STANDARTISED RURAL/URBAN VARIABLE (NO CHANGES REQUIRED, BUT PLEASE MAKE SURE THESE VARIABLES EXIST)
	gen res=urban		 // "res" is the binary variable identifying urban/rural location, where urban is 1, rural is 0
	
* GENERATE AFFILIATION AND ACKNOWLEDGEMENT VARIABLES
	gen SOURCE = "Test"  	// Specify source of data by replacing the word Test inside quotes (e.g. "MS" or "Member State")
	gen Producers="Test"     // Specify affiliation of the data analyst by replacing the word Test inside quotes (e.g. "MoH" or "WHO")
	gen dataanalyst="Test"  // Specify data analyst by replacing the word Test inside quotes (e.g. "Rose LeGood")
	gen ackn="Test"			// Specify acknowledgement by replacing the word Test inside quotes
** Example of acknowledgement:
***"Acknowledgement: Statistics Sierra Leone, Sierra Leone Integrated Household Survey (SLIHS) 2011. Ref. SLE_2011_SLIHS_v01_M." 
			
* GENERATE INFORMATION ABOUT WHEN DATA WERE ANALYSED (NO CHANGES REQUIRED, BUT PLEASE MAKE SURE THESE VARIABLES EXIST)
	global sysdate= "`c(current_date)'"
	global sysdate: subinstr global sysdate " " "", all
	display "$sysdate"
	gen analysisdate="$sysdate"

**Label variables [DO NOT MODIFY THIS SECTION]
	lab var welfare "Monetary welfare indicator" // Add label 
	lab var popweight "popweight given in the survey" // Add label
	lab var dataanalyst "Data Analyst" 		// Add label
	lab var ackn "Acknowledgement"     		// Add label
	lab var analysisdate "Analysis Date"	// Add label
	lab var HHID "Household ID"	// Add label	
	lab var hhw "Household weight"	// Add label	
	lab var hhsize "Household size"	// Add label	
	lab var cname "Country name"	// Add label	
	lab var res "Urban area, 1=yes"	// Add label	
	lab var SOURCE "Source of data"  	
	lab var Producers "Affiliation of the data analyst"     
	lab var dataanalyst "Data analyst"  
    lab var ackn "Acknowledgement"	
	lab var vdata "Data version"	
    lab var referenceid "Reference ID"	
    lab var survey "Survey acronym"	
	

*SAVE THE FINAL DATASET
saveold "$wrkdata/${iso3c}_${year}_v0s.dta",version(12) replace

********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant), Vladimir Gordeev (WHO staff) and Gabriela Flores (WHO staff).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO does not provide the data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/
