****************************************************************************************************************************************
*Objective: Export SDG 3.8.2&related statistics in excel
*********************************************************************************************************************************************
*[DO NOT MODIFY ANY CONTENT IN THIS DO FILE]


*************************************************************************************
* Version: May 24, 2025 
* Produced by WHO HQ-Financial protection monitoring team
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (CC Table 2025.do)
*****************************************************************************************/

gen keepa2_215=1
gen keepa4_25=1
gen keepa3_25=1
gen keepa2_25=1
 

gen keep1hage_25=1
gen keep2_215_IMPOV_hage_25=1
gen keep2_relPL60_IMPOV_hage_25=1

gen keep1hgender_25=1
gen keep2_215_IMPOV_hgender_25=1
gen keep2_relPL60_IMPOV_hgender_25=1

gen keep1age_25=1
gen keep2_215_IMPOV_age_25=1
gen keep2_relPL60_IMPOV_age_25=1

** replacing missing with NA, NQ
** Note: .q means NQ and .a means NA

** Missing values in case we have estimates but shoulnt be published
capture confirm variable FHD_SPL40_rur
		 if !_rc {  
		for var FHD_SPL40_rur FHD_SPL40_urb: replace X=.a if keepa4_25==1 & keepa3_25==0 
		 }
			else{
			gen FHD_SPL40_rur=.
			gen FHD_SPL40_urb=.
		    }


**** NQ
* FHD at national level
for var FHD_SPL40comp_12pop FHD_SPL40comp_3: replace X=.q if keepa4_25==1 & keepa2_25==0 & PL_SPL==PL_215

* Area residence
*replace FHD_SPL40_rur=.q if keepa3_25==0 & popsizesurvey_urb!=.
*replace FHD_SPL40_urb=.q if keepa3_25==0 & popsizesurvey_urb!=.


* Head age
capture confirm variable FHD_SPL40_h0
		 if !_rc {  
for var FHD_SPL40_h0 FHD_SPL40_h1: replace X=.q if (keep1hage_25==0 | keep2_215_IMPOV_hage_25==0 | keep2_relPL60_IMPOV_hage_25==0) & inlist(popw_g0, 99996, 99997, 99998) &  inlist(popw_g1, 99996, 99997, 99998)
		 }
			else{
			gen FHD_SPL40_h0=.
			gen FHD_SPL40_h1=.
			gen popw_h0=.
			gen popw_h1=.
			gen FHD_SPL100_h0=.
			gen FHD_SPL100_h1=.			
		    }
			

* Head gender
capture confirm variable FHD_SPL40_g0
		 if !_rc {  
for var FHD_SPL40_g0 FHD_SPL40_g1: replace X=.q if (keep1hgender_25==0 | keep2_215_IMPOV_hgender_25==0 | keep2_relPL60_IMPOV_hgender_25==0) & inlist(popw_g0, 99996, 99997, 99998) &  inlist(popw_g1, 99996, 99997, 99998)
		 }
			else{
			gen FHD_SPL40_g0=.
			gen FHD_SPL40_g1=.
			gen popw_g0=.
			gen popw_g1=.
			gen FHD_SPL100_g0=.
			gen FHD_SPL100_g1=.	
		    }
			

* HH type
capture confirm variable popw_t1
		 if !_rc {  
foreach var of varlist popw_t1-popw_t5 {
	gen `var'_idx = (`var' != 99996 & `var'!= 99997 & `var' != 99998)
}
egen tt_idx = rowtotal(popw_t?_idx)
gen popw_t_all=99996 if tt_idx == 0
drop popw_t?_idx tt_idx
for var FHD_SPL40_t*: replace X=.q if (keep1age_25==0 | keep2_215_IMPOV_age_25==0 | keep2_relPL60_IMPOV_age_25==0) & popw_t_all==99996
		 }
			else{
			    foreach name in popw_t1 popw_t2 popw_t3 popw_t4 popw_t5 FHD_SPL40_t1 FHD_SPL40_t2 FHD_SPL40_t3 FHD_SPL40_t4 FHD_SPL40_t5 FHD_SPL100_t1 FHD_SPL100_t2 FHD_SPL100_t3 FHD_SPL100_t4 FHD_SPL100_t5{
	gen `name'=.
				}
			}	


* IMPOV 2.15
for var imp_np215_pop imp_p215_pop IMPOV_215_pop: replace X=.q if ~missing(X)&keepa2_215==0


**** NA
for var imp_np215_pop imp_p215_pop IMPOV_215_pop FHD_SPL40comp_12pop FHD_SPL40comp_3: replace X=.a if missing(X)&X!=.q
for var FHD_SPL40_t* FHD_SPL40_h* FHD_SPL40_g*: replace X=.a if inlist(X,.,999999,99999,99998,99997,99996)&X!=.q
forvalues i=1/5{
replace FHD_SPL40_t`i'=.a if popw_t`i'==.a & FHD_SPL40_t`i'==.q
}
for var pos_oop_pop	sh_hexp_1_pop	hh_expcapd_med	hh_expcapd_pop	PL_SPL 	PL_215	popw_urb  popw_rur	popw_g1	popw_g0	popw_h1	popw_h0	popw_t1	popw_t2	popw_t3	popw_t4	popw_t5: replace X=.a if inlist(X,.,999999,99999,99998,99996)&X!=.q





***** REVISED SDG 3.8.2 SHEET
glo language "en"


preserve
	import excel "$dofile\Titles.xlsx", sheet("rsdg_head") firstrow clear
	mata: rsdg_head = st_sdata(., "$language")
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_t1") firstrow clear
	mata: rsdg_t1 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_t2") firstrow clear
	mata: rsdg_t2 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_t3") firstrow clear
	mata: rsdg_t3 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_t4") firstrow clear
	mata: rsdg_t4 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_t5") firstrow clear
	mata: rsdg_t5 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("rsdg_notes") firstrow clear
	mata: rsdg_notes = st_sdata(., "$language")
restore

gen year1 = year
gen null1 = .
gen null2 = .

local mata_end end

*local vlist "FHD_SPL40_pop FHD_SPL40comp_12pop FHD_SPL40comp_3pop null1 FHD_SPL40_q1 FHD_SPL40_q2 FHD_SPL40_q3 FHD_SPL40_q4 FHD_SPL40_q5 FHD_SPL40_urb FHD_SPL40_rur FHD_SPL40_t1 FHD_SPL40_t2 FHD_SPL40_t3 FHD_SPL40_t4 FHD_SPL40_t5 FHD_SPL40_g1 FHD_SPL40_g0 FHD_SPL40_h0 FHD_SPL40_h1 null2 year1 imp_np215_pop imp_p215_pop IMPOV_215_pop"
local vlist "FHD_SPL40_pop FHD_SPL40comp_3pop FHD_SPL40comp_12pop null1 FHD_SPL40_q1 FHD_SPL40_q2 FHD_SPL40_q3 FHD_SPL40_q4 FHD_SPL40_q5 FHD_SPL40_urb FHD_SPL40_rur FHD_SPL40_t1 FHD_SPL40_t2 FHD_SPL40_t3 FHD_SPL40_t4 FHD_SPL40_t5 FHD_SPL40_g1 FHD_SPL40_g0 FHD_SPL40_h0 FHD_SPL40_h1 null2 year1 imp_np215_pop imp_p215_pop IMPOV_215_pop"

levelsof iso3c, local(iso3s)
foreach iso in `iso3s' {
    preserve
		keep if iso3c == "`iso'"
		local country = cname
		local source = SOURCE
		*local region=WHO_regionCC
		local n = `c(N)'
		
		mata {
		    vars = tokens("`vlist'")
			data = st_data(., vars)
		}
	restore
	
	local filename "$root2excel/`iso'_UHC financial protection indicators 2025_EN.xlsx"
	local sheetname "Revised SDG 3.8.2"
	
	export excel year referenceid Producers welfare SPL_definition using "`filename'" if iso3c == "`iso'", sheet("`sheetname'", replace) cell(B16) missing("NA")
	
    mata
	rsdg_headc = rsdg_head
	rsdg_headc[1] = "`country' – " + rsdg_head[1]
	
    b = xl()
	
	b.load_book("`filename'")
	b.set_mode("open")
	b.set_sheet("`sheetname'")
	b.set_sheet_gridlines("`sheetname'", "off")
	
	base_row = 16
	base_col = 7
	for (i = 1; i <= rows(data); i++) {
		for (j = 1; j <= cols(data); j++) {
			if (data[i, j] == .a) {
				b.put_string(base_row + i - 1, base_col + j - 1, "NA")  
			}
			else if (data[i, j] == .q) {
				b.put_string(base_row + i - 1, base_col + j - 1, "NQ")  
			}
			else {
				b.put_number(base_row + i - 1, base_col + j - 1, data[i, j])  
			}
		}
	}
	
	b.set_font((1, 16 + `n' - 1 + 13), (1, 31), "Calibri", 14, "black")
	b.set_row_height(1, 16 + `n' - 1, 25)
	b.set_fill_pattern(2, (2, 31), "solid", "0 0 128")
	b.set_font(2, (2, 31), "Calibri", 15, "white")
	b.set_font_bold(2, (2, 31), "on")
	b.set_vertical_align(2, 2, "center")
	
	b.set_font(4, 2,  "Calibri", 14, "red")
	b.set_font_bold((4, 5), 2, "on")
	
	b.set_font_bold(7, 2, "on")
	b.set_font(5, 2,  "Calibri", 14, "21 61 100")
	b.set_font(7, 2,  "Calibri", 14, "21 61 100")
	
	b.put_string(2, 2, rsdg_headc)
	
	b.set_fill_pattern((11, 14), (7, 9), "solid", "0 0 128")
	b.set_font((11, 14), (7, 9), "Calibri", 14, "white")
	b.set_font_bold((11, 14), (7, 9), "on")
	b.set_sheet_merge("`sheetname'", (11, 11), (8, 9))
	b.set_horizontal_align(11, (7, 9), "center")
	b.put_string(11, 8, rsdg_t1)
	
	b.set_column_width(1, 1, 13)
	b.set_column_width(2, 2, 15)
	b.set_column_width(3, 3, 60)
	b.set_column_width(4, 4, 19)
	b.set_column_width(5, 5, 18)
	b.set_column_width(6, 6, 36)
	b.set_column_width(7, 7, 25)
	b.set_column_width(8, 9, 38)
	b.set_column_width(10, 10, 7)
	
	b.set_sheet_merge("`sheetname'", (12, 12), (8, 9))
	b.set_sheet_merge("`sheetname'", (13, 13), (8, 9))
	b.set_sheet_merge("`sheetname'", (14, 14), (8, 9))
	b.set_text_wrap((12, 14), 8, "on")
	b.set_row_height(12, 14, 55)
	b.set_vertical_align((12, 12), (7, 8), "top")
	b.set_vertical_align((13, 14), (7, 8), "bottom")
	b.put_string(12, 7, rsdg_t2)
	b.put_string(13, 7, rsdg_t3)
	b.put_string(14, 7, rsdg_t4)
	
	b.set_row_height(15, 15, 70)
	b.set_fill_pattern(15, (2, 6), "solid", "211 211 211")
	b.set_vertical_align((15, 16 + `n' - 1), (2, 31), "center")
	b.set_text_wrap(15, (2, 31), "on")
	b.set_font_bold(15, (2, 31), "on")
	b.put_string(15, 2, rsdg_t5)
	
	b.set_fill_pattern(15, (7, 9), "solid", "251 226 213")
	b.set_fill_pattern((14, 15), (11, 15), "solid", "218 242 208")
	b.set_fill_pattern((14, 15), (16, 17), "solid", "255 220 185")
	b.set_fill_pattern((14, 15), (18, 26), "solid", "242 206 239")
	b.set_fill_pattern((15), (29, 31), "solid", "255 230 153")
	b.set_fill_pattern(13, (11, 26), "solid", "0 0 128")
	b.set_fill_pattern((13, 14), (29, 31), "solid", "0 0 128")
	b.set_vertical_align((13, 15), (11, 31), "center")
	b.set_sheet_merge("`sheetname'", (13, 13), (11, 26))
	b.set_sheet_merge("`sheetname'", (13, 14), (29, 31))
	b.set_sheet_merge("`sheetname'", (14, 14), (11, 15))
	b.set_sheet_merge("`sheetname'", (14, 14), (16, 17))
	b.set_sheet_merge("`sheetname'", (14, 14), (18, 22))
	b.set_sheet_merge("`sheetname'", (14, 14), (23, 24))
	b.set_sheet_merge("`sheetname'", (14, 14), (25, 26))
	
	b.set_text_wrap((13, 14), (29, 31), "on")
	
	b.set_font_bold((13, 15), (11, 31), "on")
	b.set_font(13, (11, 31), "Calibri", 14, "white")
	
	// Notes
	base_row = 16 + `n'
    row_heights = (25, 65, 45, 35, 45, 25, 45, 45, 65, 45, 80, 65)
	
	for (i = 1; i <= length(row_heights); i++) {
        row_index = base_row + i
        b.set_row_height(row_index, row_index, row_heights[i])
        b.set_sheet_merge("`sheetname'", (row_index, row_index), (2, 9))
    }
	
	b.set_font_bold(base_row + 4, 2, "on")
	b.set_text_wrap((base_row + 1, base_row + length(row_heights)), (2, 9), "on")
	b.set_vertical_align((base_row + 1, base_row + length(row_heights)), (2, 9), "bottom")
	b.put_string(base_row + 1, 2, rsdg_notes)
	
	b.set_column_width(11, 30, 20)
	b.set_column_width(31, 31, 30)
	b.set_column_width(27, 27, 7)
	b.set_column_width(28, 28, 12)
	
	b.set_horizontal_align((16, 16 + `n' - 1), (2, 3), "left")
	b.set_horizontal_align((13, 16 + `n' - 1), (7, 31), "center")
	b.set_horizontal_align((13, 14), (7, 9), "left")
	b.set_number_format((16, 16 + `n' - 1), (7, 26), "number_d2")
	b.set_number_format((16, 16 + `n' - 1), (29, 31), "number_d2")
	
	b.set_border((15, 16 + `n' - 1), (2, 9), "thin")
	b.set_border((13, 16 + `n' - 1), (11, 26), "thin")
	b.set_border((13, 16 + `n' - 1), (29, 31), "thin")
	b.set_border((15, 16 + `n' - 1), 28, "thin")
	b.set_fill_pattern((15, 16 + `n' - 1), 28, "solid", "0 0 128")
	b.set_font((15, 16 + `n' - 1), 28, "Calibri", 14, "white")
	
	b.set_left_border((15, 16 + `n' - 1), 2, "thick")
	b.set_top_border(15, (2, 9), "thick")
	b.set_right_border((15, 16 + `n' - 1), 9, "thick")
	b.set_bottom_border(16 + `n' - 1, (2, 9), "thick")
	
	b.set_top_border(13, (11, 26), "thick")
	b.set_left_border((13, 16 + `n' - 1), 11, "thick")
	b.set_right_border((13, 16 + `n' - 1), 26, "thick")
	b.set_bottom_border(16 + `n' - 1, (11, 26), "thick")
	
	b.set_left_border((14, 16 + `n' - 1), 23, "thick")
	b.set_right_border((14, 16 + `n' - 1), 24, "thick")
	
	b.set_left_border((13, 14), 29, "thick")
	b.set_top_border(13, (29, 31), "thick")
	b.set_right_border((13, 16 + `n' - 1), 31, "thick")
	b.set_bottom_border(16 + `n' - 1, (28, 31), "thick")
	b.set_top_border(15, 28, "thick")
	b.set_left_border((15, 16 + `n' - 1), 28, "thick")
	
	b.close_book()
	
	`mata_end'	
}


************ ANCILLARY STATS

preserve
	import excel "$dofile\Titles.xlsx", sheet("anc_notes") firstrow clear
	mata: anc_notes = st_sdata(., "$language")
	
	import excel "$dofile\Titles.xlsx", sheet("anc_head") firstrow clear
	mata: anc_head = st_sdata(., "$language")
	
	import excel "$dofile\Titles.xlsx", sheet("anc_t1") firstrow clear
	mata: anc_t1 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("anc_t2") firstrow clear
	mata: anc_t2 = st_sdata(., "$language")'
	
	import excel "$dofile\Titles.xlsx", sheet("anc_t3") firstrow clear
	mata: anc_t3 = st_sdata(., "$language")'
restore

local mata_end end

local vlist "pos_oop_pop hh_hexpcapd_pop sh_hexp_1_pop hh_expcapd_med hh_expcapd_pop PL_SPL PL_215 popw_ur popw_rur popw_t1 popw_t2 popw_t3 popw_t4 popw_t5 popw_g1 popw_g0 popw_h0 popw_h1"

levelsof iso3c, local(iso3s)
foreach iso in `iso3s' {
    preserve
		keep if iso3c == "`iso'"
		local country = cname
		local source = SOURCE
		*local region = WHO_regionCC
		local n = `c(N)'
		
		mata {
			vars = tokens("`vlist'")
			data = st_data(., vars)
		}
	restore
	
	local filename "$root2excel/`iso'_UHC financial protection indicators 2025_EN.xlsx"
	local sheetname "Ancillary statistics"
	
	export excel year referenceid Producers welfare SPL_definition using "`filename'" if iso3c == "`iso'", sheet("`sheetname'", replace) cell(B17) missing("NA")
	
    mata
	
	anc_headc = anc_head
	anc_headc[1] = "`country' – " + anc_head[1]
	
    b = xl()
	
	b.load_book("`filename'")
	b.set_mode("open")
	b.set_sheet("`sheetname'")
	b.set_sheet_gridlines("`sheetname'", "off")
	
	base_row = 17
	base_col = 7
	for (i = 1; i <= rows(data); i++) {
		for (j = 1; j <= cols(data); j++) {
			if (data[i, j] == .a) {
				b.put_string(base_row + i - 1, base_col + j - 1, "NA")  
			}
			else {
				b.put_number(base_row + i - 1, base_col + j - 1, data[i, j])  
			}
		}
	}
	
	b.set_font((1, 17 + `n' - 1 + 18), (1, 24), "Calibri", 14, "black")
	b.set_row_height(1, 17 + `n' - 1 + 18, 20)
	b.set_fill_pattern(2, (2, 24), "solid", "0 0 128")
	b.set_font(2, (2, 24), "Calibri", 15, "white")
	b.set_font_bold(2, (2, 24), "on")
	b.set_vertical_align((1, 17 + `n' - 1), (1, 24), "center")
	
	b.set_font(4, 2,  "Calibri", 14, "red")
	b.set_font_bold((4, 7), 2, "on")
	b.set_font(5, 2, "Calibri", 14, "0 0 128")
	b.put_string(2, 2, anc_headc)
	
	b.set_font_bold((14, 16), (2, 24), "on")
	b.set_fill_pattern((14, 15), (7, 9), "solid", "144 238 144")
	b.set_fill_pattern((14, 15), (10, 11), "solid", "153 255 204")
	b.set_fill_pattern((14, 15), (12, 13), "solid", "192 230 245")
	b.set_fill_pattern((14, 15), (14, 15), "solid", "255 220 185")
	b.set_fill_pattern((14, 15), (16, 24), "solid", "242 206 239")
	b.set_fill_pattern(16, (2, 6), "solid", "211 211 211")
	
	b.set_sheet_merge("`sheetname'", (14, 14), (7, 9))
	b.set_sheet_merge("`sheetname'", (14, 14), (10, 11))
	b.set_sheet_merge("`sheetname'", (15, 15), (10, 11))
	b.set_sheet_merge("`sheetname'", (14, 14), (12, 13))
	b.set_sheet_merge("`sheetname'", (14, 14), (14, 15))
	b.set_sheet_merge("`sheetname'", (14, 14), (16, 20))
	b.set_sheet_merge("`sheetname'", (14, 14), (21, 22))
	b.set_sheet_merge("`sheetname'", (14, 14), (23, 24))
	b.set_horizontal_align((14, 17 + `n' - 1), (7, 24), "center")
	b.set_row_height(14, 14, 90)
	b.set_row_height(15, 15, 150)
	b.put_string(14, 7, anc_t1)
	b.put_string(15, 7, anc_t2)
	
	b.set_column_width(1, 1, 15)
	b.set_column_width(2, 2, 10)
	b.set_column_width(3, 3, 45)
	b.set_column_width(4, 5, 20)
	b.set_column_width(6, 6, 35)
	b.set_column_width(7, 13, 24)
	b.set_column_width(14, 20, 16)
	b.set_column_width(21, 24, 12)
	
	b.set_text_wrap((14, 16), (2, 24), "on")
	
	b.set_row_height(16, 16, 150)
	b.set_sheet_merge("`sheetname'", (16, 16), (14, 15))
	b.set_sheet_merge("`sheetname'", (16, 16), (16, 20))
	b.set_sheet_merge("`sheetname'", (16, 16), (21, 22))
	b.set_sheet_merge("`sheetname'", (16, 16), (23, 24))
	b.set_horizontal_align(16, (7, 24), "center")
	b.put_string(16, 2, anc_t3)
	b.set_horizontal_align((17, 17 + `n' - 1), 2, "left")
	
	// Notes
	base_row = 17 + `n'
    row_heights = (35, 45, 20, 20, 45, 90, 45, 45, 45, 80)

    for (i = 1; i <= length(row_heights); i++) {
        row_index = base_row + i
        b.set_row_height(row_index, row_index, row_heights[i])
        b.set_sheet_merge("`sheetname'", (row_index, row_index), (2, 10))
    }

    b.set_font_bold(base_row + 4, 2, "on")
    b.set_text_wrap((base_row + 1, base_row + length(row_heights)), (2, 10), "on")
	
	b.put_string(base_row + 1, 2, anc_notes)
	
	// set borders
	b.set_border((14, 17 + `n' - 1), (7, 24), "thin")
	b.set_border((16, 17 + `n' - 1), (2, 6), "thin")
	b.set_top_border(14, (7, 24), "thick")
	b.set_right_border((14, 17 + `n' - 1), 24, "thick")
	b.set_bottom_border(17 + `n' - 1, (2, 24), "thick")
	b.set_left_border((16, 17 + `n' - 1), 2, "thick")
	b.set_top_border(16, (2, 6), "thick")
	b.set_left_border((14, 15), 7, "thick")
	
	b.set_bottom_border(16, (2, 24), "thick")
	
	b.set_number_format((17, 17 + `n' - 1), (7, 24), "number_d2")
		
	b.close_book()
	
	`mata_end'	
}



**** Data table sheet
	glo iso=iso3c
	
keep iso3c year referenceid	Producer	welfare	SPL_definition	FHD_SPL40_pop	FHD_SPL40comp_12pop	FHD_SPL40comp_3pop	country		popsizesurvey		hh_expcapd_med	PL_215	PL_SPL	relPL50g	relPL50	hh_expcapd_pop	hh_hexpcapd_pop	sh_hexp_1_pop	pos_oop_pop	P0_SPL_exp_rur	P0_SPL_exp_urb	P0_SPL_exp_q1	P0_SPL_exp_q2	P0_SPL_exp_q3	P0_SPL_exp_q4	P0_SPL_exp_q5	P0_SPLg_exp_pop	P0_SPLg_exp_rur	P0_SPLg_exp_urb	P0_SPLg_exp_q1	P0_SPLg_exp_q2	P0_SPLg_exp_q3	P0_SPLg_exp_q4	P0_SPLg_exp_q5	tm_1_pop	tm_7_pop		FHD_SPL100_pop	FHD_SPL100_rur	FHD_SPL100_urb	FHD_SPL100_q1	FHD_SPL100_q2	FHD_SPL100_q3	FHD_SPL100_q4	FHD_SPL100_q5	FHD_SPL100_t1	FHD_SPL100_t2	FHD_SPL100_t3	FHD_SPL100_t4	FHD_SPL100_t5		FHD_SPL100_h0	FHD_SPL100_h1	FHD_SPL100_g0	FHD_SPL100_g1 FHD_SPL40_rur	FHD_SPL40_urb	FHD_SPL40_q1	FHD_SPL40_q2	FHD_SPL40_q3	FHD_SPL40_q4	FHD_SPL40_q5	FHD_SPL40_t1	FHD_SPL40_t2	FHD_SPL40_t3	FHD_SPL40_t4	FHD_SPL40_t5	FHD_SPL40_h0	FHD_SPL40_h1	FHD_SPL40_g0	FHD_SPL40_g1	fhtSPL40comp_1	fhtSPL40comp_2	fhtSPL40comp_3	popw_ur	popw_rur	popw_t1	popw_t2	popw_t3	popw_t4	popw_t5	popw_g0	popw_g1	popw_h0	popw_h1	imp_np215_pop	imp_p215_pop	IMPOV_215_pop


export excel using "$root2excel/${iso3c}_UHC financial protection indicators 2025_EN.xlsx", sheet("Result data table") sheetreplace firstrow(variables)



********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO does not provide the data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/