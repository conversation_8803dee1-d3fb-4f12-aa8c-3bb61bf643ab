****************************************************************************************************************************************
*Objective: Produce revised SDG 3.8.2 & related statistics at national level, by quintiles and where possible, for  rural/urban areas of residence
*********************************************************************************************************************************************
*[DO NOT MODIFY ANY CONTENT IN  THIS DO FILE]
 
* Steps:
	* Step 0: Generating key variables
	* Step 1: SPL
	* Step 2: Generate basic needs and discretionary consumption/income
	* STEP 3: Decompose new definition at 10% (tm7), 25% (tm6), and 40% (tm7)
	* STEP 4: Produce statistics
	* STEP 5: Label variables
	* STEP 6: Rename variables
	
*************************************************************************************
* Version: May 24, 2025 
* Produced by WHO HQ-Financial protection monitoring team
* Based on: PFCC_patchextended.do 
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (FPCC_SDG382revShort_MS.do)
*****************************************************************************************/


*************************  STEP 0) Generating key variables       *******************
glo iso3c=iso3c
glo year=year

*Create a multiplier variable based on recall period
			*gen recall_multiplier = .
			replace recall_multiplier = 1           if standard_pds == 0   // daily
			replace recall_multiplier = 1/7         if standard_pds == 1   // weekly
			replace recall_multiplier = 12/365      if standard_pds == 2   // monthly
			replace recall_multiplier = 1/365       if standard_pds == 3   // annual
			replace recall_multiplier = . if missing(standard_pds)

* calculate expenditure variables in per day values
		   foreach var of varlist exp hexp {
		   replace `var' = `var' * recall_multiplier
		   label variable `var' "hhd `var' (LCU/day)"
		   }
		   
		   // drop if negative expenditures
			  drop if exp<0
		   // drop if there is no food expenditure 
			  drop if fexp==0

* calculate expenditure variables in per capita 
		   local n=1
		   foreach name in exp hexp {
		   local n1 "hh total exp"
		   local n2 "hh health care exp"
		   gen hh_`name'capd=`name'/hhsize
		   lab var hh_`name'capd "`n`n'' per capita (LCU/cap/d)"
		   local n=`n'+1
		   }
		   
gen hh_nexpcapd = hh_expcapd - hh_hexpcapd if ~missing(hh_expcapd) & hh_expcapd >= hh_hexpcapd
lab var hh_nexpcapd "hh exp net of oop (LCU/cap/d)"

/*egen hh_expcapd_med_pop = wpctile(hh_expcapd), p(50) weights(popweight)
lab var hh_expcapd_med_pop "median hh total expendture (LCU/cap/d)"
*/

sum hh_expcapd [w=popweight], de
gen hh_expcapd_med_pop =r(p50)
lab var hh_expcapd_med_pop "hh median exp on health (LCU/cap/d)"

*************************    STEP 1) SPL *******************************
************************************************************************
* generate the relative component of the SPL (50% median + 1.15 in 2017 PPP) /*Nov 22, 2024 description corrected */ 
* first the median is based on consumption/income NET of OOPs  /*Nov 22, 2024  added */

// calculate the relative PL as a 60%/50% of the median hhd total expenditures
gen relPL50g = 0.5 * hh_expcapd_med_pop
lab var relPL50g "relative poverty line, ie. 50% of median consumption gross of OOP"

/*egen hh_nexpcapd_med_pop = wpctile(hh_nexpcapd), p(50) weights(popweight)
lab var hh_nexpcapd_med_pop "median hh total expendture net of oop (LCU/cap/d)"
*/
sum hh_nexpcapd [w=popweight], de
gen hh_nexpcapd_med_pop =r(p50)
lab var hh_nexpcapd_med_pop "hh median exp on health (LCU/cap/d)"

// calculate the relative poverty line (PL) as a 50% or 60% of the median HHD total expenditures net of oop
gen relPL50 = 0.5 * hh_nexpcapd_med_pop
lab var relPL50 "relative poverty line, ie. 50% of median consumption NET of OOPs"

			   capture confirm variable res  // CK: removed IDN 14/03/2025
			   if !_rc{
				display "variable res available"
				if  inlist(iso3c,"IND","CHN"){
					local rururb_ratio = PPPrur2017/PPPurb2017 
					
				* medians  based on net consumption expenditure
					gen     harmonised_cons = hh_nexpcapd 																	// CK: added 25 FEB
					replace harmonised_cons = hh_nexpcapd  / `rururb_ratio' if res==0           // harmonised to urban		// CK: changed 25 FEB
					sum     harmonised_cons [aw=popweight] , d																
					replace  hh_nexpcapd_med_pop =r(p50)				 	if res==1 & inlist(iso3c,"IND","CHN")			// CK: added 25 FEB
					replace  hh_nexpcapd_med_pop =r(p50)*`rururb_ratio'		if res==0 & inlist(iso3c,"IND","CHN")			// CK: added 25 FEB		


				   replace relPL50 = 0.5*hh_nexpcapd_med_pop if  inlist(iso3c,"IND","CHN")									// CK: added 25 FEB

			   
			   * medians based on total consumption 
					gen     harmonised_consg = hh_expcapd 																	// CK: added 25 FEB
					replace harmonised_consg = hh_expcapd / `rururb_ratio' 	if res==0           // harmonised to urban		// CK: changed 25 FEB
					sum     harmonised_consg [aw=popweight] , d
					replace  hh_expcapd_med_pop =r(p50)						if res==1 & inlist(iso3c,"IND","CHN")			// CK: added 25 FEB
					replace  hh_expcapd_med_pop =r(p50)*`rururb_ratio'		if res==0 & inlist(iso3c,"IND","CHN")			// CK: added 25 FEB		           


					replace relPL50g = 0.5*hh_expcapd_med_pop if  inlist(iso3c,"IND","CHN")									// CK: added 25 FEB

			   
				}
		}
		else{
						display "variable res NOT available"

		}

gen P0_215_exp = cond(missing(hh_expcapd), ., cond(hh_expcapd < PL_215, 1, 0))
gen P0_215_nexp = cond(missing(hh_nexpcapd), ., cond(hh_nexpcapd < PL_215, 1, 0))
lab variable P0_215_exp "(exp) poverty indicator at the extreme poverty line"
lab variable P0_215_nexp "(exp net) poverty indicator at the extreme poverty line"


gen pos_oop = cond(hexp > 0, 1, 0)
lab var pos_oop "hh with OOP spending"

* create quintiles daily of per capita consumption	   
xtile quintile_pc=hh_expcapd [aw=popweight], nq(5)
lab var quintile_pc "quintiles' rank of per capita daily hh exp"

* OOP ratio in total expenditures of the hhd
gen sh_hexp_1=cond(hexp/exp<=1, hexp/exp,.) 
lab var sh_hexp_1 "share of health expenditure over total expenditure (LCU/d)"

gen double SPLPPP2017 = relPL50 + (PPP2017 * 1.15)
label variable SPLPPP2017 "Joliffe&Prydz Societal poverty line: $1.15+50% median consumption (net of oop) in 2017" /* label corrected on Nov 13, 2024 */

* the relative component is based on the median cross of OOPs /*Nov 22, 2024  added */
gen double SPLPPP2017g = relPL50g + (PPP2017 * 1.15)
label variable SPLPPP2017g "Joliffe&Prydz Societal poverty line: $1.15+50% median consumption (gross of oop) in 2017"




* Relative component of the SPL : rur - urb /* Nov 22,2024  updated */
capture confirm variable res
if !_rc {
    display "variable res available"
    replace SPLPPP2017 = relPL50 + 1.15 * PPPrur2017 if res == 0 & inlist(iso3c, "CHN", "IDN") /* corrected on Nov 13, 2024: res==0 instead of res==1 */
    replace SPLPPP2017 = relPL50 + 1.15 * PPPurb2017 if res == 1 & inlist(iso3c, "CHN", "IDN") /* corrected on Nov 13, 2024: res==1 instead of res==0 */
	* 2 lines below added on Nov 13, 2024 and udpated on Nov 22, 2024
    replace SPLPPP2017g = relPL50g + 1.15 * PPPrur2017 if res == 0 & inlist(iso3c, "CHN", "IDN") /* corrected on Nov 13, 2024: res==0 instead of res==1 */
    replace SPLPPP2017g = relPL50g + 1.15 * PPPurb2017 if res == 1 & inlist(iso3c, "CHN", "IDN") /* corrected on Nov 13, 2024: res==1 instead of res==0 */
}
else {
    display "variable res NOT available"
}



*generate PL_SPL 
gen PL_SPL=PL_215 if max(PL_215,SPLPPP2017)==PL_215
replace PL_SPL=SPLPPP2017 if max(PL_215,SPLPPP2017)==SPLPPP2017
	* 2 lines below added on Nov 13, 2024
	gen PL_SPLg=PL_215 if max(PL_215,SPLPPP2017g)==PL_215
	replace PL_SPLg=SPLPPP2017g if max(PL_215,SPLPPP2017g)==SPLPPP2017g

* Create categories based on hh_expcapd

******** POOR AT THE RELATIVE COMPONENT OF THE SPL ($1.15+50% OF MEDIAN) /* Nov 22,2024 UPDATED */
gen double P0_SPL2017_exp=cond(missing(hh_expcapd), ., cond(hh_expcapd<=SPLPPP2017,1,0))
lab var P0_SPL2017_exp "FGT0: poverty headcount, exp net of OOP ($1.15 + 50%median)" /* label corrected on Nov 13, 2024 */
	* 2 lines below added on Nov 13, 2024 updated on Nov 22, 2024
	gen double P0_SPL2017g_exp=cond(missing(hh_expcapd), ., cond(hh_expcapd<=SPLPPP2017g,1,0))
	lab var P0_SPL2017g_exp "FGT0: poverty headcount, exp gross of OOP ($1.15 + 50%median)"


gen double P0_SPLlb215_exp=.
replace    P0_SPLlb215_exp=P0_SPL2017_exp if PL_SPL==SPLPPP2017
replace    P0_SPLlb215_exp=P0_215_exp 	  if PL_SPL==PL_215
	* 3 lines below added on Nov 13, 2024, updated on Nov 22, 2024	
		gen double P0_SPLlb215g_exp=.
		replace    P0_SPLlb215g_exp=P0_SPL2017g_exp if PL_SPLg==SPLPPP2017g 
		replace    P0_SPLlb215g_exp=P0_215_exp 	  if PL_SPLg==PL_215	
			

*****    STEP 2) Generate basic needs and discretionary consumption/income *******************************
**********************************************************************************************************
/*Based needs budget share and discretionary budget share
gen sh_PLSPL =PL_SPL/hh_expcapd
gen sh_dspl=1-sh_PLSPL
*/
* Create thresholds as a function of the discretionary amounts
gen tm_1=max(0,(hh_expcapd-PL_SPL))
gen tm_7=0.4*tm_1

******** Generate SDG 3.8.2 based on the discretionary amounts
* IMPOV (OOP> 100% of the discretionary amounts)
local i=1
gen SDG382_discontinuous_tm`i' = 0
replace SDG382_discontinuous_tm`i' = 1 if hh_hexpcapd > tm_`i' &~missing(hh_hexpcapd)&~missing(tm_`i' )

* Different gradients applied: OOP> 10%, 25%, 40% of discretionary amounts
local i=7
    gen SDG382_discontinuousalt_tm`i' = 0
    replace SDG382_discontinuousalt_tm`i' =1 if hh_hexpcapd > tm_`i' &~missing(hh_hexpcapd)&~missing(tm_`i' )



***** STEP 3) Decompose new definition at 10% (tm7), 25% (tm6), and 40% (tm7) *******************************
*************************************************************************************************************
label define fhspl 1 "further pushed" 2 "pushed" 3 "cata" 0 "no financial hardship" -1 "no OOPs", modify

local i=7

    local name7=40
		gen     FINHARDT_SPL`name`i''=1 if (SDG382_discontinuous_tm1==1 & tm_1==0)
		replace FINHARDT_SPL`name`i''=FINHARDT_SPL`name`i'' * pos_oop
		replace FINHARDT_SPL`name`i''=2 if (SDG382_discontinuous_tm1==1 & tm_1~=0)
		replace FINHARDT_SPL`name`i''=3 if SDG382_discontinuous_tm1==0 & SDG382_discontinuousalt_tm`i'==1
		replace FINHARDT_SPL`name`i''=0 if SDG382_discontinuousalt_tm`i'==0
   label values FINHARDT_SPL`name`i'' fhspl


************************************ STEP 4) Produce statistics ******************************************
**********************************************************************************************************
capture confirm variable SDG382_discontinuous_tm1
if !_rc {
    display "variable SDG382_discontinuousalt_tm1 available"

* average monetary threshold values
foreach var of varlist tm_1  tm_7 {
	sum `var' [w=popweight]
	gen `var'_pop = r(mean)
    }
}



* proportion of total population incurring financial hardship (all categories)
* 13 Nov 2024: correction to add SDG382_discontinuousalt_tm7 to the below var list
foreach var of varlist P0_SPLlb215g_exp P0_SPLlb215_exp SDG382_discontinuous_tm1  SDG382_discontinuousalt_tm7 {
	sum `var' [w=popweight]
	gen `var'_pop=r(mean)*100
}

* 25 Nov 2025: added stats among those spending
foreach var of varlist SDG382_discontinuous_tm1  SDG382_discontinuousalt_tm7 {
	sum `var' [w=popweight] if pos_oop==1
	gen `var'_pos=r(mean)*100
}

* by area of residence: rural/urban
capture confirm variable res
if !_rc {
    forvalues k=0/1 {
        local r0 "rur"
        local r1 "urb"
        foreach var of varlist P0_SPLlb215g_exp P0_SPLlb215_exp SDG382_discontinuous_tm1  SDG382_discontinuousalt_tm7 {
            sum `var' [w=popweight] if res == `k'
            gen `var'_`r`k'' = r(mean) * 100
        }
		
		 foreach var of varlist hh_expcapd {
            sum `var' [w=popweight] if res == `k'
            gen `var'_`r`k'' = r(mean)
        }
		
    }
	

    foreach name in rur urb {
        label variable hh_expcapd_`name' "(`name') average CONS or INC (LCU/cap/day)"
    }
}
else {
    display "rural/urban info not available"
}

* by quintiles
forvalues q=1/5{
foreach var of varlist P0_SPLlb215g_exp P0_SPLlb215_exp SDG382_discontinuous_tm1  SDG382_discontinuousalt_tm7{
	sum `var' [w=popweight] if quintile_pc==`q'
	gen `var'_q`q'=r(mean)*100
	}
}

forvalues q=1/5{
foreach var of varlist hh_expcapd{
	sum `var' [w=popweight] if quintile_pc==`q'
	gen `var'_q`q'=r(mean)
	}
}

* Decomposing the total number of people incurring financial hardship into the different categories (A) to (E):
svyset _n [pweight = popweight]
local j=7
    local name7=40
	capture confirm variable FINHARDT_SPL`name`j''
	if !_rc {
    qui svy: tab FINHARDT_SPL`name`j'' if SDG382_discontinuousalt_tm`j'==1
    matrix fhtSPL`name`j''_=e(b)*100
    svmat  fhtSPL`name`j''_
    local k=1
		levelsof FINHARDT_SPL`name`j'' if SDG382_discontinuousalt_tm`j'==1, clean local(hhfhtype)
		foreach i of local hhfhtype {
			rename fhtSPL`name`j''_`k' fhtSPL`name`j''comp_`i'
			local k=`k'+1
		}
}
else {
    display "FINHARD_SPL not available"
}

svyset, clear


************************************ STEP 5) Label variables******************************************
*******************************************************************************************************

label variable PL_SPL "Societal poverty line max($2.15;$1.15+50% NET median)"
label variable PL_SPLg "Societal poverty line max($2.15;$1.15+50% NET median)" /* added on Nov 13, 2024 */
label variable P0_SPL2017_exp "Poor at $1.15+50% median NET of OOP" /* corrected on Nov 13, 2024 */
label variable P0_SPL2017g_exp "Poor at $1.15+50% median gross of OOP" /* added on Nov 13, 202 updated on Nov 22, 2024 */
label variable P0_SPLlb215_exp_pop "Poor at SPL max($2.15;$1.15+50% NET median)" /* corrected on Nov 13, 2024 */
label variable P0_SPLlb215g_exp_pop "Poor at SPL max($2.15;$1.15+50% gross median)" /* corrected on Nov 13, 2024 */

/*
label variable sh_PLSPL_pop "SPL basic needs budget share" /* corrected on Nov 13, 2024 */
label variable sh_dspl_pop "Discretionary budget share (SPL based)" /* corrected on Nov 13, 2024 */
*/

label variable tm_1 "threshold=100% of discretionary CONS or INC"
label variable tm_7 "threshold=40% of discretionary CONS or INC"

/* corrected on Nov 13, 2024 to add _pop */
label variable SDG382_discontinuous_tm1_pop "OOP>100% of discretionary CONS or INC"
label variable SDG382_discontinuousalt_tm7_pop "OOP>40% of discretionary CONS or INC"

/* added on Nov 25, 2024 */
label variable SDG382_discontinuous_tm1_pos "(among spenders) OOP>100% of discretionary CONS or INC"
label variable SDG382_discontinuousalt_tm7_pos "(among spenders) OOP>40% of discretionary CONS or INC"


label variable FINHARDT_SPL40 "TYPE OF FIN HARD AT SPL&40%"

label variable tm_1_pop "(pop) threshold=100% of discretionary CONS or INC"
label variable tm_7_pop "(pop) threshold=40% of discretionary CONS or INC"

/*
label variable sh_PLSPL_pop "(pop) SPL basic needs budget share"
label variable sh_dspl_pop "(pop) Discretionary budget share (SPL based NET OF OOP)" /* corrected on Nov 13, 2024 */
*/

label variable P0_SPLlb215_exp_pop "(pop) poverty headcount rate at SPL (net of OOP)" /* corrected on Nov 13, 2024 */
label variable P0_SPLlb215g_exp_pop "(pop) poverty headcount rate at SPL (gross of OOP)" /* added on Nov 13, 2024 */

foreach num of numlist 40 {
    capture confirm variable fhtSPL`num'comp_1
    if !_rc {
        label variable fhtSPL`num'comp_1 "(SPL-further pushed) discretionary CONS or INC is negative & OOP>0"
    }
    else {
        display "variable fhtSPL`num'comp_1 missing"
    }

    capture confirm variable fhtSPL`num'comp_2
    if !_rc {
        label variable fhtSPL`num'comp_2 "(SPL-pushed) discretionary CONS or INC is positive & OOP>100% disc CONS or INC"
    }
    else {
        display "variable fhtSPL`num'comp_2 missing"
    }

    capture confirm variable fhtSPL`num'comp_3
    if !_rc {
        label variable fhtSPL`num'comp_3 "(SPL) discretionary CONS or INC is positive & OOP>`num'% disc CONS or INC"
    }
    else {
        display "variable fhtSPL`num'comp_3 missing"
    }
}


rename hh_expcapd_med_pop hh_expcapd_med
label variable hh_expcapd_med "(pop) median CONS or INC (LCU/cap/day)"

* additional labels added on Nov 13, 2024:
foreach name in q1 q2 q3 q4 q5 {
	label variable P0_SPLlb215_exp_`name' "(`name') poverty headcount rate at SPL (net of OOP)" /* corrected on Nov 13, 2024 */
	label variable P0_SPLlb215g_exp_`name' "(`name') poverty headcount rate at SPL (gross of OOP)" /* added on Nov 13, 2024 */
	label variable SDG382_discontinuous_tm1_`name' "(`name') OOP>100% of discretionary CONS or INC"
	label variable SDG382_discontinuousalt_tm7_`name' "(`name') OOP>40% of discretionary CONS or INC"
}
capture confirm variable res
if !_rc {
	capture assert mi(res)
	if _rc==0 {
		disp "res is all missing"
	}
	else {
		display "dataset has a variable with information on area of residence (rural/urban)"
		foreach name in rur urb {
			label variable P0_SPLlb215_exp_`name' "(`name') poverty headcount rate at SPL (net of OOP)" /* corrected on Nov 13, 2024 */
			label variable P0_SPLlb215g_exp_`name' "(`name') poverty headcount rate at SPL (gross of OOP)" /* added on Nov 13, 2024 */
			label variable SDG382_discontinuous_tm1_`name' "(`name') OOP>100% of discretionary CONS or INC"
		label variable SDG382_discontinuousalt_tm7_`name' "(`name') OOP>40% of discretionary CONS or INC"
		}
	}
}

************************************ STEP 6) rename  variables to facilitate the consolidation with the World Bank******************************************
*******************************************************************************************************

* rename variables /* added Nov 22, 2024 */
rename P0_SPLlb215_exp P0_SPL_exp
rename P0_SPLlb215g_exp P0_SPLg_exp
rename SDG382_discontinuous_tm1 SDG382_FHSPL100
rename SDG382_discontinuousalt_tm7 SDG382_FHSPL40

* rename stats
rename P0_SPLlb215_exp_pop P0_SPL_exp_pop
rename P0_SPLlb215g_exp_pop P0_SPLg_exp_pop

rename SDG382_discontinuous_tm1_pop SDG382_FHSPL100_pop
rename SDG382_discontinuousalt_tm7_pop SDG382_FHSPL40_pop

* added on Nov 25, 2024
rename SDG382_discontinuous_tm1_pos SDG382_FHSPL100_pos
rename SDG382_discontinuousalt_tm7_pos SDG382_FHSPL40_pos

capture confirm variable res
if !_rc {
	/*capture assert mi(res)
	if _rc==0 {
		disp "res is all missing"
	}
	else {*/
		display "dataset has a variable with information on area of residence (rural/urban)"
		rename P0_SPLlb215_exp_rur P0_SPL_exp_rur
		rename P0_SPLlb215g_exp_rur P0_SPLg_exp_rur
		rename SDG382_discontinuous_tm1_rur SDG382_FHSPL100_rur
		rename SDG382_discontinuousalt_tm7_rur SDG382_FHSPL40_rur

		rename P0_SPLlb215_exp_urb P0_SPL_exp_urb
		rename P0_SPLlb215g_exp_urb P0_SPLg_exp_urb
		rename SDG382_discontinuous_tm1_urb SDG382_FHSPL100_urb
		rename SDG382_discontinuousalt_tm7_urb SDG382_FHSPL40_urb
	}
	else{
		disp "res variable in the dataset"
	}


rename P0_SPLlb215_exp_q1 P0_SPL_exp_q1
rename P0_SPLlb215g_exp_q1 P0_SPLg_exp_q1
rename SDG382_discontinuous_tm1_q1 SDG382_FHSPL100_q1
rename SDG382_discontinuousalt_tm7_q1 SDG382_FHSPL40_q1

rename P0_SPLlb215_exp_q2 P0_SPL_exp_q2
rename P0_SPLlb215g_exp_q2 P0_SPLg_exp_q2
rename SDG382_discontinuous_tm1_q2 SDG382_FHSPL100_q2
rename SDG382_discontinuousalt_tm7_q2 SDG382_FHSPL40_q2

rename P0_SPLlb215_exp_q3 P0_SPL_exp_q3
rename P0_SPLlb215g_exp_q3 P0_SPLg_exp_q3
rename SDG382_discontinuous_tm1_q3 SDG382_FHSPL100_q3
rename SDG382_discontinuousalt_tm7_q3 SDG382_FHSPL40_q3

rename P0_SPLlb215_exp_q4 P0_SPL_exp_q4
rename P0_SPLlb215g_exp_q4 P0_SPLg_exp_q4
rename SDG382_discontinuous_tm1_q4 SDG382_FHSPL100_q4
rename SDG382_discontinuousalt_tm7_q4 SDG382_FHSPL40_q4

rename P0_SPLlb215_exp_q5 P0_SPL_exp_q5
rename P0_SPLlb215g_exp_q5 P0_SPLg_exp_q5
rename SDG382_discontinuous_tm1_q5 SDG382_FHSPL100_q5
rename SDG382_discontinuousalt_tm7_q5 SDG382_FHSPL40_q5


**************************** Add more variables************************
gen FHD_SPL40comp_3=SDG382_FHSPL40-SDG382_FHSPL100
lab var FHD_SPL40comp_3 "with positive OOP > 40% but < 100% of discretionary budget (facing large but not impoverishing OOP)"

gen FHD_SPL40comp_12=SDG382_FHSPL100
lab var FHD_SPL40comp_12 "with positive OOP >= 100% of discretionary budget (facing large and impoverishing OOP)"



// Identify households below the relative poverty lines and gap to the poverty line when household welfare measure is total expenditure or income gross of health spending (exp) versus net of health spending (nexp) 
foreach name in exp nexp {
	gen P0_relPL50_`name'=cond(missing(hh_`name'capd), ., cond(hh_`name'capd<relPL50, 1, 0))
	gen P0_relPL50g_`name'=cond(missing(hh_`name'capd), ., cond(hh_`name'capd<relPL50g, 1, 0))

}

// identifies changes in poverty status due to health spending at the household level
gen imp_nprelPL50 = P0_relPL50_nexp - P0_relPL50_exp


// identifies poor people spending on health out-of-pocket
foreach num of numlist 50 {
	gen imp_prelPL`num'=cond(missing(hh_expcapd), ., cond(hh_expcapd<relPL`num' & hh_hexpcapd>0 & ~missing(hh_hexpcapd), 1, 0))
	lab var imp_prelPL`num' "poor and OOP>0 (`num' % of median cons)"
}

// pushed and further pushed
foreach num of numlist 50 {
	gen IMPOV_relPL`num'=(imp_nprelPL`num'==1|imp_prelPL`num'==1)
	lab var IMPOV_relPL`num' "impoverished & further impoverished, due to OOP (relPL$`num')"
}
		   
// label variables
lab var P0_relPL50_exp "Below the poverty line (50% of median cons) given per capita daily exp"
lab var P0_relPL50_nexp "Below the poverty line (50% of median cons) given per capita daily exp net of OOPs"
lab var imp_nprelPL50 "impoverished, due to OOPs (50% of median cons)"	 
lab var P0_relPL50g_exp "Below the poverty line (50% of median cons gross of OOP) given per capita daily exp"
lab var P0_relPL50g_nexp "Below the poverty line (50% of median cons gross of OOP) given per capita daily exp net of OOPs"
		   	

// Identifies changes in poverty status due to health spending
	   		   
gen imp_p215=cond(missing(hh_expcapd), ., cond(hh_expcapd<PL_215 & hh_hexpcapd>0 & ~missing(hh_hexpcapd), 1, 0))
gen imp_np215=P0_215_nexp-P0_215_exp
gen IMPOV_215=(imp_np215==1|imp_p215==1)


foreach var of varlist pos_oop  sh_hexp_1 hh_expcapd hh_hexpcapd P0_215_exp imp_p215 imp_np215{
	sum `var' [w=popweight]
	gen `var'_pop = r(mean)
    }


foreach var of varlist FHD_SPL40comp_3 FHD_SPL40comp_12{
	sum `var' [w=popweight]
	gen `var'pop = r(mean)

    }
	lab var FHD_SPL40comp_12pop "(pop) % facing large and impoverishing OOP"
lab var FHD_SPL40comp_3pop "(pop) % facing large but not impoverishing OOP"


gen IMPOV_215_pop=imp_np215_pop+imp_p215_pop if imp_np215_pop!=. & imp_np215_pop !=.

for var IMPOV_215_pop imp_np215_pop imp_p215_pop pos_oop_pop sh_hexp_1_pop FHD_SPL40comp_*pop: replace X=X*100 

svyset [w=popweight], clear
svy: mean hh_expcapd 
gen popsizesurvey=e(N_pop)
label variable popsizesurvey "population size (sample weights)"		  


capture confirm variable res
if !_rc {
	capture assert mi(res)
	if _rc==0 {
		disp "res variable is not missing but only has misssing values"
			gen popsizesurvey_urb=.
			gen popw_urb=.
			gen popw_rur=.
	}
	else {
			disp "res variable is not missing and has values"
			svy: mean hh_expcapd if res==1
			gen popsizesurvey_urb=e(N_pop)
			gen popw_urb=popsizesurvey_urb/popsizesurvey*100
			gen popw_rur=100-popw_urb
			lab var popw_rur "(rur) % of pop"
						lab var popw_urb "(urb) % of pop"
						label variable popsizesurvey_urb "(urb) nb of people"
		 }

}
			else {
		disp "res variable is missing"
		    }
	
	
* SPL definition
gen SPL_definition="PPP2017$1.15+50%med_net" 
replace SPL_definition="PPP2017$2.15" if PL_215 == PL_SPL
lab var SPL_definition "Societal poverty line definition"
* rename
rename SDG382_FH* FHD_*

lab var pos_oop_pop "(pop) hh with OOP spending" 
lab var sh_hexp_1_pop "(pop) share of health expenditure over total expenditure (LCU/d)" 
lab var hh_expcapd_pop "(pop) hh consumption (LCU/cap/d)" 
lab var hh_hexpcapd_pop "(pop) hh OOP health exp (LCU/cap/d)" 
lab var imp_p215_pop "(pop) further impoverished by OOP at 2.15" 
lab var imp_np215_pop "(pop) impoverished by OOP at 2.15"
lab var IMPOV_215_pop "(pop) impoverished + further impov by OOP at 2.15"


********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant), Vladimir Gordeev (WHO staff) and Gabriela Flores (WHO staff).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO does not provide the data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/