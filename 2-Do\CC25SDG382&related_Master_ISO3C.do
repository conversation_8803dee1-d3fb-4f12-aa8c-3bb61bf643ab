/************************************************************************************
* Running Master DO file 
***************************************************************************************
* Objectives:
* Produce estimates SDG 3.8.2 indicator and its components, where possible disaggregated

* Version: May 23, 2025
* Acknowledgement, disclaimer and other issues related to this do file, please see at the end
*********************************************************************************************
* CONTACT DETAILS: <EMAIL> , subject (01_Prepare Do file)
*****************************************************************************************/

*-----------------------------------------------------------------------------------------------------------
*Code structure
*-----------------------------------------------------------------------------------------------------------

* Preliminaries: memory, additional commands/packages           [MIGHT REQUIRE MANUAL INPUTS]
* Define USER, paths to datasets and folders, open log file     [REQUIRES MANUAL INPUTS]
* Define the list of surveys (e.g., ISO codes and years) and specify their respective microdataset preparation scripts  [REQUIRES MANUAL INPUTS]
* Produce financial protection indicators                       [REQUIRES MANUAL INPUTS]
* List of extended microdatasets (denoted mesoallv)				[REQUIRES MANUAL INPUTS]
* Disaggregation of results by HHD characteristics              [REQUIRES MANUAL INPUTS]
* Save final mesodata										    [REQUIRES MANUAL INPUTS]
* Export results                       							[DO NOT MODIFY THIS SECTION]

*************************************************************************
* Preliminaries: cleaning memory and installing packaged  [MIGHT REQUIRE MANUAL INPUTS]
**************************************************************************

clear all
macro drop _all
*set matsize 8000, permanent  /*  for Stata/MP and Stata/SE only  comment out if you do not have one of those versions */
*set maxvar 20000, permanent  /*  for Stata/MP and Stata/SE only  comment out if you do not have one of those versions */
set more off, permanent
capture log close

*-----------------------------------------------------------------------------------------------------------------------------
* Install additional commands - depends on your STATA version  [MIGHT REQUIRE MANUAL INPUTS]
*-----------------------------------------------------------------------------------------------------------------------------
*Note: This section is deactivated by default
*      You can active this section by deleting "/*" and "*/" in this subsection below

/*
ssc install missings, replace   /* Stata version 9 or up is required */
ssc install tabstatmat, replace /* Stata version 8 or up is required */
ssc install distinct, replace   /* Stata version 8 or up is required */
search gr0034    /*a window will pop up, select install gr0034 Stata version 7 or up is required */
*/

*************************************************************************
* Define user and paths to datasets and folders, log file   [REQUIRES MANUAL INPUTS]
*************************************************************************
*IMPORTANT: Paths are user specific and require modification
*			For MAC users please change the "\" to "/".

*-----------------------------------------------------------------------------------------------------------------------------
*WINDOWS / MAC USER ACCOUNT NAME   [REQUIRES MANUAL INPUTS]
*-----------------------------------------------------------------------------------------------------------------------------

glo user "."     //Replace "." by the Windows/MAC user account name. For example, in a path like: "C:\Users\<USER>\Documents\", the username is "johndoe"

*-----------------------------------------------------------------------------------------------------------------------------
* Path to the microdata repository - :  this path is user specific -  [REQUIRES MANUAL INPUTS]  FOR MAC USERS PLEASE CHANGE the "\" to "/".
*-----------------------------------------------------------------------------------------------------------------------------
// In the below line, replace "\World Health Organization\WHO Financial protection monitoring - Sandbox\RawData" by the relevant path to your folder with the raw microdata in your computer

global rawdata "C:\Users/${user}\World Health Organization\WHO Financial protection monitoring - Sandbox\RawData"

*-----------------------------------------------------------------------------------------------------------------------------
* Path to the folder: STATA PACKAGE May 2025  [REQUIRES MANUAL INPUTS]
* precondition:  copy the folder "STATA PACKAGE May 2025" in a dedicated folder
*-----------------------------------------------------------------------------------------------------------------------------


*SPECIFY LOCATION OF THE SDG382 PACKAGE  [REQUIRES MANUAL INPUTS]
//In the below line, replace "\World Health Organization\WHO Financial protection monitoring - Sandbox\Stata packages\CC2025\STATA codes for MS" by the relevant path to the forlder 	including the stata package

	glo mypersonalpath "C:\Users/${user}\World Health Organization\WHO Financial protection monitoring - Sandbox\Stata packages\CC2025\STATA codes for MS"


*DEFINE THE PATH TO SUBFORLDER IN THE STATA PACKAGE May 2025   [FOR WINDOWS USERS: NO NEED TO CHANGE  // FOR MAC USERS: PLEASE CHANGE the "\" to "/"]
	glo root0      "${mypersonalpath}\STATA PACKAGE May 2025"
	glo wrkdata    "${root0}\0-Working data"             //The ISO dataset is saved here. This folder will also include the data ready to produce the SDG 3.8.2 and related indicators
	glo do2        "${root0}\2-Do"  					 //This Master do file and a template do file to prepare the key variables to produce SDG 3.8.2&related indictors are saved here.
														 //Please also save in here any other do file you may use to prepare the data.
	glo dofile     "${root0}\3-WHO standardized do MS"   //This folder stored standardized DO files [IMPORTANT - DO NOT MODIFY]
	glo root2      "${root0}\4-Results"                  //This folder stores outputs in 2 dedicated sub-folders (Excel and Meso_data)
	glo root2meso  "${root2}\Meso_data"                  //This folder stores output: meso data
	glo root2excel "${root2}\Excel"                      //This folder stores output: Excel file

*-----------------------------------------------------------------------------------------------------------------------------
* Open a log file  [DO NOT MODIFY THIS SECTION]
*-----------------------------------------------------------------------------------------------------------------------------

	set more off
	cap log close
	global sysdate= "`c(current_date)'"
	global sysdate: subinstr global sysdate " " "", all
	display in red "$sysdate"
	log using "$root2\FPindicators_${user}_${sysdate}.log" , replace

******************************************************************************************************************
* Define the list of surveys (e.g., ISO codes and years) and specify their respective microdataset preparation scripts  [REQUIRES MANUAL INPUTS]
******************************************************************************************************************

*-----------------------------------------------------------------------------------------------------------------------------
* Surveys to be processed  [REQUIRES MANUAL INPUTS]
*-----------------------------------------------------------------------------------------------------------------------------
* in the below command, replace ISO_2009 by iso3c_year (e.g. for Belize 2018 BLZ_2018). Use the first year of the survey (see WHOFP-STATS - data requirements - May 2025.doc)
* if you have more than one year of data use blanks to separate them
* e.g. BLZ_2018 PER_2018 MEX_2018 ISO_YYYY ISO_ZZZZ ...
* the macro "country_y" is used in the rest of the codes to call out the datasets to be processed

	#delimit ;
	global country_y "
	ISO_2009
	";
	#delimit cr

*-----------------------------------------------------------------------------------------------------------------------------
* Microdataset Preparation  [DO NOT MODIFY THIS SECTION]
*-----------------------------------------------------------------------------------------------------------------------------
* This section calls a country-specific preparation do-file (01_Prepare_ISO3C_YYYY_v0s), which must be customized beforehand.
*-----------------------------------------------------------------------------------------------------------------------------

* If following WHO recommendations, the do-file should be named: 01_Prepare_ISO3C_YYYY_v0 (e.g. 01_Prepare_PER_2021_v0), where:
*    - ISO3C is the country's ISO 3-letter code,
*    - YYYY is the survey year,
*    - v0 indicates the data version.

* Please ensure the contents of "01_Prepare_ISO3C_YYYY.do" have been reviewed and updated for your dataset.
* We recommend ISOing the adapted template using the sample datasets in the $wrkdata folder.

* You may SKIP this section if:
* - Your microdata already includes all key variables, and
* - You have followed the WHO naming conventions described in: "WHOFP-STATS - data requirements - May 2025".

* Call the preparation do-file:
	foreach y in $country_y {
		do "${do2}/01_Prepare_`y'_v0s.do"
	}



**********************************************************************************************************************
* PRODUCE FINANCIAL PROTECTION INDICATORS (SDG 3.8.2 & related)                       [DO NOT MODIFY THIS SECTION]
**********************************************************************************************************************
*Note: The output of this section is called mesoallv_`i' or mesoa_`i' with i=1,....T

glo idvar "iso3c year referenceid Producer welfare SPL_definition SOURCE cname survey vdata dataanalyst ackn analysisdate"

foreach y in $country_y {
use "$wrkdata/`y'_v0s.dta", clear
do   "$dofile/FPCC_SDG382revShort_MS.do"
save "${root2meso}/${iso3c}_mesoallv_${year}.dta",replace
keep $idvar *pop *med *q* *rur *urb *PL*
save "${root2meso}/${iso3c}_meso_${year}.dta",replace
}

*********************************************************************************************
* LIST ALL MESO DATASETS [REQUIRES MANUAL INPUTS]
* PLEASE UPDATE THE LIST TO INCLUDE ALL THE MESOALL TO BE PROCESSED
* IN THE REST OF THE DO FILE, THIS LIST WILL ALWAYS BE USED
*********************************************************************************************
***In the below, replace "ISO_mesoallv_2009" with appropriate list.
****For example, if you have data for 2017, 2021, 2025, the list will be  (ISO_mesoallv_2018 ISO_mesoallv_2021 ISO_mesoallv_2025)

#delimit ;
global mesoall "
ISO_mesoallv_2009
";
#delimit cr


**********************************************************************************************************************
* DISAGGREGATION OF financial protection indicators (SDG 3.8.2 & related) BY HOUSEHOLD CHARACTERISTICS  [DO NOT MODIFY THIS SECTION]
**********************************************************************************************************************

*-----------------------------------------------------------------------------------------------------------------------------
* BY AGE / GENDER COMPOSITION OF THE HHD MEMBERS   [DO NOT MODIFY THIS SECTION]
*-----------------------------------------------------------------------------------------------------------------------------
*Note: * The output of this section is called mesoallv_`i' or mesoa_`i' with i=1,....T

foreach y in $mesoall {
use "${root2meso}/`y'.dta",clear
do "$dofile\FPCC_HHD typology.do"
do "$dofile\FPCC_agedisaggregationn123.do"
glo iso3c=iso3c
glo year=year
save "${root2meso}/${iso3c}_mesoallv_${year}.dta",replace
keep $idvar *pop *med *q* *rur *urb *t* *h* *g* *flag* *check* hhw_* popw_* *PL*
save "${root2meso}/${iso3c}_meso_${year}.dta",replace
}

**********************************************************************************************************************
* IF YOU PRODUCED INDICATORS FOR ONLY ONE YEAR RENAME THE MESODATA [DOES NOT REQUIRE MANUAL INPUTS] - DEFAULT OPTION
* IF YOU PRODUCED INDICATORS FOR SEVERAL YEARS: APPEND ALL THE MESODATA AND RENAME [REQUIRES MANUAL INPUTS] 
**********************************************************************************************************************

*----------------------------------------------------------------------------------------------------------------
* Only one year [DOES NOT REQUIRE MANUAL INPUTS] - DEFAULT OPTION
*----------------------------------------------------------------------------------------------------------------


* rename the microdata that includes only the statistics at national level and by quintile and if applicable by other household characteristics and survey identification variables
use "${root2meso}/${iso3c}_meso_${year}.dta",clear
save "${root2meso}/${iso3c}_meso.dta", replace
erase "${root2meso}/${iso3c}_meso_${year}.dta"

* rename the extended microdata set which now includes the statistics at national level and by quintile and if applicable by other household characteristics

use "${root2meso}/${iso3c}_mesoallv_${year}.dta",clear
save "${root2meso}/${iso3c}_mesoallv.dta",replace
erase "${root2meso}/${iso3c}_mesoallv_${year}.dta"

*----------------------------------------------------------------------------------------------------------------
* Multiple years  [REQUIRES MANUAL INPUTS] 
*----------------------------------------------------------------------------------------------------------------
* COMMENT OUT THIS SECTION IF YOU HAVE MULTIPLE YEARS
/*


* append and rename the microdata that includes only the statistics at national level and by quintile and if applicable by other household characteristics and survey identification variables
foreach name in ISO_meso_2009{		/* insert all years processed. For example, if you have data for 2018, 2021, 2025, the list will be  (ISO_meso_2018 ISO_meso_2021 ISO_meso_2025) */
append using "${root2meso}/`name'.dta" 	/* no need to modify this line */
}
save "${root2meso}/${iso3c}_meso.dta", replace


*erase all year specific meso
foreach name in ISO_meso_2009{		/* insert all years processed. For example, if you have data for 2018, 2021, 2025, the list will be  (ISO_meso_2018 ISO_meso_2021 ISO_meso_2025) */
erase  "${root2meso}/`name'.dta" 	/* no need to modify this line */
}

* [DEFAULT ASSUMPTION] your dataset is not a panel, so there is no need to append the extended microdata sets (${iso3c}_mesoallv_${year}).
* if your dataset is a panel, comment out the commands hereafter to create one consolidated dataset by appending  all years ${iso3c}_mesoallv_${year} 
/*
foreach name in ISO_mesoallv_2009{		/* insert all years processed. For example, if you have data for 2018, 2021, 2025, the list will be  (ISO_mesoallv_2018 ISO_mesoallv_2021 ISO_mesoallv_2025) */
append using "${root2meso}/`name'.dta" 	/* no need to modify this line */
}
save "${root2meso}/${iso3c}_mesoallv.dta", replace
*/


*/

**********************************************************************************************************************
* EXPORT RESULTS [DO NOT MODIFY THIS SECTION]
**********************************************************************************************************************

use "$root2meso/${iso3c}_meso.dta", clear

*-----------------------------------------------------------------------------------------------------------------------------
*CREATE VARIABLE WITH COUNTY NAME, YEAR, REFERENCE [DO NOT MODIFY THIS SECTION]
*---------------------------------------------------------------------------------
glo iso3c=iso3c
glo year=year
egen country_year= concat(cname year)
duplicates drop country_year, force
save "${root2meso}/${iso3c}_meso.dta", replace

*-----------------------------------------------------------------------------------------------------------------------------
*EXPORT RESULTS TABLE
*-----------------------------------------------------------------------------------------------------------------------------
do "$dofile\CC Table 2025.do"


********************************************************************************
* [END]
********************************************************************************

*---------------------------------------------------------------------------------------------------------
* Acknowledgment and use of WHO name
*---------------------------------------------------------------------------------------------------------
/*
1. This version of the do file was updated by Asiyeh Abbasi (WHO consultant), Vladimir Gordeev (WHO staff) and Gabriela Flores (WHO staff).

2. For any mention of the WHO STATA do files, use of outputs, and/or use of the Data, in publications (including reports, briefings, and journal articles) you must include the following citation of the source:
Financial Protection Statistics - SDG 3.8.2 Tracking Assessment Tool (WHO FP-STATS): STATA DO FILES. Geneva, World Health Organization, 2025.

3.	as WHO only provides Test data to use this do file, the user shall not state or imply that results are WHO's products, opinion, or statements. Further, you shall not (i) in connection with your use of the do files, state or imply that WHO endorses or is affiliated with you or your use of the Tool, the Software, or the Data, or that WHO endorses any entity, organization, company, or product. All requests to use the WHO name and/or emblem require advance written approval of WHO.
*/

*---------------------------------------------------------------------------------------------------------
* Disclaimers by WHO
*---------------------------------------------------------------------------------------------------------
/*
1.	No WHO warranties. WHO makes no warranty with respect to the do file(s), and disclaims all statutory or implied warranties, expressed or implied, as to the accuracy, completeness or usefulness of any information, apparatus, product, or process related to the do file(s), including, without limitation, to any warranty of design or fitness for a particular purpose, even if WHO has been informed of such purpose. WHO does not represent that the use of the do file(s) would not infringe third parties' proprietary rights. WHO provides the do file(s) "as is", and does not represent that the do file(s) is operational, free of defects, virus free, able to operate on an uninterrupted basis, or appropriate for your technical system.

2. Country or area designations. The designations employed and the presentation of the material in the Observatory do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delimitation of its frontiers or boundaries.

3. Mentions of companies or products. Any mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by the World Health Organization in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
*/

*---------------------------------------------------------------------------------------------------------
* Your Data and the DO file(s)
*---------------------------------------------------------------------------------------------------------
/*
1.	By using the do file(s), you confirm that all data that you upload to, or use in, the do file(s) is either owned by you or, if not, you have obtained all necessary and relevant permissions to use the data in the do file(s), and that WHO has no responsibility or control over the data you use in that regard. You confirm that you will not use any data to the do file(s) which is personal information or data or would in any way be in violation of law, including privacy and intellectual property law.

*---------------------------------------------------------------------------------------------------------
* General Provisions 
*---------------------------------------------------------------------------------------------------------
/*
Nothing contained herein or in any license or terms of use related to the subject matter herein shall be construed as a waiver of any of the privileges and immunities enjoyed by the World Health Organization under national or international law, and/or as submitting the World Health Organization to any national court jurisdiction.
*/
